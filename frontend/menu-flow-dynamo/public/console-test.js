/**
 * BROWSER CONSOLE TEST SCRIPT
 * SME Analytica Frontend Testing
 * 
 * Copy and paste this script into the browser console on any page
 * to test the frontend functionality
 */

console.log('🚀 SME Analytica Frontend Console Test Suite');
console.log('📅 Test Time:', new Date().toLocaleString());

// Test Configuration
const TEST_CONFIG = {
  restaurantId: '01aaadc0-bf00-47bf-b642-5c1fa8a3b912',
  tableId: '3abb9efe-5e5f-460d-b574-7c2bd82ba7d7',
  baseUrl: window.location.origin
};

// Test Results
let testResults = {
  passed: 0,
  failed: 0,
  tests: []
};

function logTest(name, status, details = '') {
  const result = `${status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⚠️'} ${name}: ${status} ${details}`;
  testResults.tests.push(result);
  if (status === 'PASS') testResults.passed++;
  if (status === 'FAIL') testResults.failed++;
  console.log(result);
}

// TEST 1: Current Time and Menu Availability Logic
function testTimeLogic() {
  console.log('\n🕐 Testing Time Logic...');
  
  const now = new Date();
  const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
  
  logTest('Current Time', 'INFO', currentTime);
  
  // Test the isTimeInRange function logic
  const testCases = [
    { time: currentTime, start: '00:00:00', end: '23:59:59', expected: true, name: 'All Day Menu' },
    { time: currentTime, start: '00:00:00', end: '23:00:00', expected: currentTime <= '23:00', name: 'Old All Day Menu' },
    { time: currentTime, start: '20:00:00', end: '23:00:00', expected: currentTime >= '20:00' && currentTime <= '23:00', name: 'Dinner Menu' }
  ];
  
  testCases.forEach(test => {
    const result = isTimeInRange(test.time, test.start, test.end);
    const status = result === test.expected ? 'PASS' : 'FAIL';
    logTest(`Time Range ${test.name}`, status, `${test.time} in ${test.start}-${test.end}: ${result}`);
  });
}

// Helper function (copy of the one from menuService)
function isTimeInRange(time, startTime, endTime) {
  if (startTime > endTime) {
    return time >= startTime || time <= endTime;
  }
  return time >= startTime && time <= endTime;
}

// TEST 2: DOM Elements Check
function testDOMElements() {
  console.log('\n🔍 Testing DOM Elements...');
  
  // Check for menu items
  const menuItems = document.querySelectorAll('[data-testid="menu-item"], .menu-item, [class*="menu-item"]');
  const menuCards = document.querySelectorAll('.card, [class*="card"]');
  const addButtons = document.querySelectorAll('[class*="add"], button[class*="cart"], [data-testid="add-to-cart"]');
  
  logTest('Menu Items Found', menuItems.length > 0 ? 'PASS' : 'FAIL', `${menuItems.length} items`);
  logTest('Menu Cards Found', menuCards.length > 0 ? 'PASS' : 'PARTIAL', `${menuCards.length} cards`);
  logTest('Add Buttons Found', addButtons.length > 0 ? 'PASS' : 'FAIL', `${addButtons.length} buttons`);
  
  // Check for error messages
  const errorMessages = document.querySelectorAll('[class*="error"], [class*="unavailable"]');
  const hasErrorText = document.body.textContent.includes('No menu items found') || 
                      document.body.textContent.includes('Menu Currently Unavailable');
  
  if (hasErrorText || errorMessages.length > 0) {
    logTest('Error Messages', 'FAIL', 'Menu unavailable messages found');
  } else {
    logTest('Error Messages', 'PASS', 'No error messages found');
  }
}

// TEST 3: Network Requests
function testNetworkRequests() {
  console.log('\n🌐 Testing Network Requests...');
  
  const resources = performance.getEntriesByType('resource');
  const apiCalls = resources.filter(r => r.name.includes('/api/') || r.name.includes('supabase'));
  const wsConnections = resources.filter(r => r.name.includes('ws://') || r.name.includes('wss://'));
  
  logTest('API Calls', apiCalls.length > 0 ? 'PASS' : 'PARTIAL', `${apiCalls.length} API requests`);
  logTest('WebSocket Connections', wsConnections.length > 0 ? 'PASS' : 'PARTIAL', `${wsConnections.length} WS connections`);
  
  // Check for failed requests
  const failedRequests = resources.filter(r => r.responseStatus >= 400);
  logTest('Failed Requests', failedRequests.length === 0 ? 'PASS' : 'FAIL', `${failedRequests.length} failed requests`);
}

// TEST 4: Console Errors
function testConsoleErrors() {
  console.log('\n🐛 Testing Console Errors...');
  
  // This is a simplified check - in a real scenario you'd need to override console methods
  const errorCount = window.console._errorCount || 0;
  logTest('Console Errors', errorCount === 0 ? 'PASS' : 'FAIL', `${errorCount} errors detected`);
}

// TEST 5: Page Performance
function testPerformance() {
  console.log('\n⚡ Testing Performance...');
  
  const navigation = performance.getEntriesByType('navigation')[0];
  if (navigation) {
    const loadTime = navigation.loadEventEnd - navigation.loadEventStart;
    const domTime = navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart;
    
    logTest('Page Load Time', loadTime < 3000 ? 'PASS' : 'PARTIAL', `${loadTime.toFixed(0)}ms`);
    logTest('DOM Load Time', domTime < 1000 ? 'PASS' : 'PARTIAL', `${domTime.toFixed(0)}ms`);
  }
}

// TEST 6: Local Storage and State
function testLocalStorage() {
  console.log('\n💾 Testing Local Storage...');
  
  const hasLocalStorage = typeof Storage !== 'undefined';
  logTest('Local Storage Support', hasLocalStorage ? 'PASS' : 'FAIL', '');
  
  if (hasLocalStorage) {
    const keys = Object.keys(localStorage);
    logTest('Local Storage Keys', 'INFO', `${keys.length} keys: ${keys.join(', ')}`);
  }
}

// Main Test Runner
async function runConsoleTests() {
  console.log('\n🎯 Running Console Test Suite...\n');
  
  testTimeLogic();
  testDOMElements();
  testNetworkRequests();
  testConsoleErrors();
  testPerformance();
  testLocalStorage();
  
  // Generate Report
  console.log('\n📊 CONSOLE TEST REPORT');
  console.log('='.repeat(40));
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  console.log(`📊 Total: ${testResults.tests.length}`);
  console.log(`🎯 Success Rate: ${((testResults.passed / testResults.tests.length) * 100).toFixed(1)}%`);
  
  console.log('\n📋 Detailed Results:');
  testResults.tests.forEach(test => console.log(test));
  
  return testResults;
}

// Auto-run if on menu page
if (window.location.pathname.includes('/menu')) {
  console.log('🍽️ Menu page detected - running tests in 2 seconds...');
  setTimeout(runConsoleTests, 2000);
} else {
  console.log('📝 Console test suite loaded. Run runConsoleTests() to execute.');
}

// Export for manual use
window.runConsoleTests = runConsoleTests;
window.testConfig = TEST_CONFIG;
