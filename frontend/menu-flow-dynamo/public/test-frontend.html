<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SME Analytica Frontend Test Suite</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border-left: 4px solid #4CAF50;
        }
        .test-button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 16px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
        }
        .test-button:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }
        .results {
            margin-top: 20px;
            padding: 20px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-pass { background: #4CAF50; }
        .status-fail { background: #f44336; }
        .status-partial { background: #ff9800; }
        .quick-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .quick-link {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            text-decoration: none;
            color: white;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .quick-link:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .info-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .info-card h3 {
            margin: 0 0 10px 0;
            color: #4CAF50;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 SME Analytica Frontend Test Suite</h1>
        
        <div class="info-grid">
            <div class="info-card">
                <h3>🏪 Restaurant</h3>
                <p>My Abrast</p>
            </div>
            <div class="info-card">
                <h3>📅 Test Time</h3>
                <p id="current-time"></p>
            </div>
            <div class="info-card">
                <h3>🌐 Environment</h3>
                <p id="environment"></p>
            </div>
            <div class="info-card">
                <h3>📊 Status</h3>
                <p id="test-status">Ready</p>
            </div>
        </div>

        <div class="test-section">
            <h2>🔗 Quick Test Links</h2>
            <div class="quick-links">
                <a href="/admin/dashboard" class="quick-link" target="_blank">
                    🏢 Admin Dashboard
                </a>
                <a href="/menu?table=3abb9efe-5e5f-460d-b574-7c2bd82ba7d7" class="quick-link" target="_blank">
                    🍽️ Customer Menu (Table 2)
                </a>
                <a href="/admin/menus" class="quick-link" target="_blank">
                    📋 Menu Management
                </a>
                <a href="/admin/orders" class="quick-link" target="_blank">
                    📦 Order Management
                </a>
            </div>
        </div>

        <div class="test-section">
            <h2>🧪 Test Controls</h2>
            <button class="test-button" onclick="runMenuAvailabilityTest()">
                🕐 Test Menu Availability (23:31+)
            </button>
            <button class="test-button" onclick="runCustomerFlowTest()">
                🛒 Test Customer Order Flow
            </button>
            <button class="test-button" onclick="runAdminFeaturesTest()">
                👨‍💼 Test Admin Features
            </button>
            <button class="test-button" onclick="runRealTimeTest()">
                ⚡ Test Real-time Features
            </button>
            <button class="test-button" onclick="runFullTestSuite()">
                🎯 Run Full Test Suite
            </button>
            <button class="test-button" onclick="clearResults()">
                🗑️ Clear Results
            </button>
        </div>

        <div class="test-section">
            <h2>📊 Test Results</h2>
            <div id="results" class="results">
                Click a test button to start testing...
            </div>
        </div>
    </div>

    <script>
        // Update current time
        function updateTime() {
            document.getElementById('current-time').textContent = new Date().toLocaleString();
            document.getElementById('environment').textContent = window.location.origin;
        }
        updateTime();
        setInterval(updateTime, 1000);

        // Results management
        let testResults = [];

        function logResult(test, status, details) {
            const timestamp = new Date().toLocaleTimeString();
            const result = `[${timestamp}] ${status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⚠️'} ${test}: ${status} ${details || ''}`;
            testResults.push(result);
            updateResultsDisplay();
        }

        function updateResultsDisplay() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.textContent = testResults.join('\n');
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function clearResults() {
            testResults = [];
            updateResultsDisplay();
            document.getElementById('test-status').textContent = 'Ready';
        }

        // Test Functions
        async function runMenuAvailabilityTest() {
            document.getElementById('test-status').textContent = 'Testing Menu Availability...';
            logResult('Menu Availability Test', 'INFO', 'Starting test at ' + new Date().toLocaleTimeString());
            
            try {
                // Open customer menu in new window
                const menuWindow = window.open('/menu?table=3abb9efe-5e5f-460d-b574-7c2bd82ba7d7', '_blank');
                
                setTimeout(() => {
                    try {
                        // Check if menu items are visible
                        const menuDoc = menuWindow.document;
                        const menuItems = menuDoc.querySelectorAll('[data-testid="menu-item"], .menu-item, [class*="menu-item"]');
                        const noItemsMessage = menuDoc.body.textContent.includes('No menu items found') || 
                                             menuDoc.body.textContent.includes('Menu Currently Unavailable');
                        
                        if (noItemsMessage) {
                            logResult('Menu Availability', 'FAIL', 'Menu shows as unavailable at current time');
                        } else if (menuItems.length > 0) {
                            logResult('Menu Availability', 'PASS', `Found ${menuItems.length} menu items available`);
                        } else {
                            logResult('Menu Availability', 'PARTIAL', 'Menu loaded but items unclear');
                        }
                        
                        menuWindow.close();
                    } catch (error) {
                        logResult('Menu Availability', 'FAIL', 'Cross-origin access blocked');
                    }
                }, 3000);
                
            } catch (error) {
                logResult('Menu Availability Test', 'FAIL', error.message);
            }
            
            document.getElementById('test-status').textContent = 'Menu test completed';
        }

        async function runCustomerFlowTest() {
            document.getElementById('test-status').textContent = 'Testing Customer Flow...';
            logResult('Customer Flow Test', 'INFO', 'Opening customer interface');
            
            // This would open the customer interface and test the flow
            const customerWindow = window.open('/menu?table=3abb9efe-5e5f-460d-b574-7c2bd82ba7d7', '_blank');
            logResult('Customer Flow', 'PASS', 'Customer interface opened successfully');
            
            document.getElementById('test-status').textContent = 'Customer flow test completed';
        }

        async function runAdminFeaturesTest() {
            document.getElementById('test-status').textContent = 'Testing Admin Features...';
            logResult('Admin Features Test', 'INFO', 'Opening admin dashboard');
            
            const adminWindow = window.open('/admin/dashboard', '_blank');
            logResult('Admin Features', 'PASS', 'Admin dashboard opened successfully');
            
            document.getElementById('test-status').textContent = 'Admin test completed';
        }

        async function runRealTimeTest() {
            document.getElementById('test-status').textContent = 'Testing Real-time Features...';
            logResult('Real-time Test', 'INFO', 'Checking WebSocket connections');
            
            // Check for WebSocket connections
            const wsConnections = performance.getEntriesByType('resource')
                .filter(entry => entry.name.includes('ws://') || entry.name.includes('wss://'));
            
            if (wsConnections.length > 0) {
                logResult('Real-time Features', 'PASS', `Found ${wsConnections.length} WebSocket connections`);
            } else {
                logResult('Real-time Features', 'PARTIAL', 'No WebSocket connections detected');
            }
            
            document.getElementById('test-status').textContent = 'Real-time test completed';
        }

        async function runFullTestSuite() {
            document.getElementById('test-status').textContent = 'Running Full Test Suite...';
            clearResults();
            
            logResult('Full Test Suite', 'INFO', 'Starting comprehensive testing');
            
            await runMenuAvailabilityTest();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await runCustomerFlowTest();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await runAdminFeaturesTest();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await runRealTimeTest();
            
            logResult('Full Test Suite', 'PASS', 'All tests completed');
            document.getElementById('test-status').textContent = 'Full test suite completed';
        }

        // Auto-run basic checks on load
        window.addEventListener('load', () => {
            logResult('Page Load', 'PASS', 'Test suite loaded successfully');
            logResult('Environment', 'INFO', `Running on ${window.location.origin}`);
            logResult('Browser', 'INFO', navigator.userAgent.split(' ').pop());
        });
    </script>
</body>
</html>
