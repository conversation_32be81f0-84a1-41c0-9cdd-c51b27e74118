-- Enable Row Level Security on menus table
ALTER TABLE public.menus ENABLE ROW LEVEL SECURITY;

-- Allow read access only to users who own the restaurant
CREATE POLICY "Read menus owned by user's restaurant"
ON public.menus FOR SELECT
USING (
  auth.uid() = (
    SELECT user_id FROM public.restaurants WHERE restaurants.id = menus.restaurant_id
  )
);

-- Allow insert access only to users who own the restaurant
CREATE POLICY "Insert menus for user's restaurant"
ON public.menus FOR INSERT
WITH CHECK (
  auth.uid() = (
    SELECT user_id FROM public.restaurants WHERE restaurants.id = menus.restaurant_id
  )
);

-- Allow update access only to users who own the restaurant
CREATE POLICY "Update menus owned by user's restaurant"
ON public.menus FOR UPDATE
USING (
  auth.uid() = (
    SELECT user_id FROM public.restaurants WHERE restaurants.id = menus.restaurant_id
  )
);

-- Allow delete access only to users who own the restaurant
CREATE POLICY "Delete menus owned by user's restaurant"
ON public.menus FOR DELETE
USING (
  auth.uid() = (
    SELECT user_id FROM public.restaurants WHERE restaurants.id = menus.restaurant_id
  )
);

-- Add public read policy for QR code functionality (customers need anonymous access)
CREATE POLICY "menus_public_read_for_qr_codes"
ON public.menus FOR SELECT
TO anon, authenticated
USING (true);

-- Add index to improve performance of restaurant_id lookups
CREATE INDEX IF NOT EXISTS idx_menus_restaurant_id ON public.menus(restaurant_id);
