
import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import AdminLayout from '@/components/AdminLayout';
import { useLanguage } from '@/contexts/LanguageContext';
import { useAuth } from '@/contexts/AuthContext';
import { getTrafficData, TrafficData } from '@/services/trafficService';
import { getMenuAnalytics, getSalesAnalytics, getRecommendedPriceAdjustments, MenuItemAnalytics, SalesAnalytics } from '@/services/analyticsService';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { Button } from '@/components/ui/button';
import { Line, LineChart, ResponsiveContainer, Tooltip, XAxis, YAxis, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'recharts';
import { getPricingRules } from '@/services/dynamicPricingService';
import type { PricingRules } from '@/services/dynamicPricingService';
import { Badge } from '@/components/ui/badge';
import { Clock, TrendingUp, TrendingDown, Users, DollarSign, Utensils, Calendar, ChevronRight } from 'lucide-react';
import CustomerSentimentAnalytics from '@/components/CustomerSentimentAnalytics';

// Define interfaces outside the component
interface PriceRecommendation {
  itemId: string;
  name: string;
  currentPrice: number;
  suggestedPrice: number;
  adjustment: number;
  reason: string;
}

const Analytics = () => {
  const { t } = useLanguage();
  const { user } = useAuth();
  const [trafficData, setTrafficData] = useState<TrafficData | null>(null);
  const [menuData, setMenuData] = useState<MenuItemAnalytics[]>([]);
  const [salesData, setSalesData] = useState<SalesAnalytics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [pricingRules, setPricingRules] = useState<PricingRules | null>(null);
  const [activeTab, setActiveTab] = useState('traffic');
  const [timeRange, setTimeRange] = useState<'day' | 'week' | 'month'>('week');
  const [priceRecommendations, setPriceRecommendations] = useState<PriceRecommendation[]>([]);
  
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        console.log('-------- ANALYTICS PAGE DATA FETCH --------');
        console.log('Current time range:', timeRange);
        
        // Get traffic data
        const data = await getTrafficData(undefined, user);
        console.log('Traffic data:', data);
        setTrafficData(data);
        
        // Get pricing rules
        const rules = await getPricingRules(undefined, user);
        console.log('Pricing rules:', rules);
        setPricingRules(rules);
        
        // Get menu analytics data
        console.log(`Fetching menu analytics for time range: ${timeRange}`);
        const menuAnalytics = await getMenuAnalytics(undefined, user, timeRange);
        console.log('Menu analytics received:', menuAnalytics);
        setMenuData(menuAnalytics);
        
        // Get sales analytics data
        const salesAnalytics = await getSalesAnalytics(undefined, user, timeRange);
        console.log('Sales analytics received:', salesAnalytics);
        setSalesData(salesAnalytics);
        
        // Generate price recommendations based on current data
        console.log('Generating price recommendations');
        console.log('- Has traffic data:', !!data);
        console.log('- Current occupancy:', data?.current_occupancy);
        console.log('- Menu items count:', menuAnalytics.length);
        console.log('- Has pricing rules:', !!rules);
        
        // Always generate some recommendations even without perfect data
        const defaultOccupancy = data?.current_occupancy !== undefined ? data.current_occupancy : 0.5; // Default to medium traffic
        const recommendations = getRecommendedPriceAdjustments(
          menuAnalytics,
          defaultOccupancy,
          rules?.highTraffic?.threshold || 0.8,
          rules?.mediumTraffic?.threshold || 0.5,
          rules?.lowTraffic?.threshold || 0.3
        );
        console.log('Price recommendations generated:', recommendations);
        setPriceRecommendations(recommendations);
      } catch (error) {
        console.error('Error fetching analytics data:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchData();
    
    // Refresh data every 5 minutes
    const refreshInterval = setInterval(fetchData, 5 * 60 * 1000);
    return () => clearInterval(refreshInterval);
  }, [user, timeRange]);
  
  // Format hourly trend data for charts
  const formatHourlyData = (data: TrafficData | null) => {
    if (!data || !data.hourly_trend || data.hourly_trend.length === 0) {
      return [];
    }
    
    return data.hourly_trend.map(point => ({
      time: new Date(point.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
      occupancy: Math.round(point.occupancy_percentage * 100),
      tables: point.active_tables,
      total: point.total_tables
    }));
  };
  
  // Format daily trend data for charts
  const formatDailyData = (data: TrafficData | null) => {
    if (!data || !data.daily_trend || data.daily_trend.length === 0) {
      return [];
    }
    
    return data.daily_trend.map(point => ({
      day: new Date(point.day).toLocaleDateString([], { weekday: 'short' }),
      occupancy: Math.round(point.average_occupancy * 100)
    }));
  };

  // Calculate current pricing status based on traffic
  const getPricingStatus = () => {
    if (!trafficData || !pricingRules) return { status: 'normal', percentage: 0 };
    
    const currentTraffic = trafficData.current_occupancy;
    
    if (currentTraffic >= (pricingRules.highTraffic?.threshold || 0.8)) {
      return { 
        status: 'high', 
        percentage: pricingRules.highTraffic?.percentage || 10,
        color: 'text-red-500',
        bgColor: 'bg-red-100'
      };
    } else if (currentTraffic >= (pricingRules.mediumTraffic?.threshold || 0.5)) {
      return { 
        status: 'medium', 
        percentage: pricingRules.mediumTraffic?.percentage || 5,
        color: 'text-amber-500',
        bgColor: 'bg-amber-100'
      };
    } else if (currentTraffic <= (pricingRules.lowTraffic?.threshold || 0.3)) {
      return { 
        status: 'low', 
        percentage: pricingRules.lowTraffic?.percentage || -5,
        color: 'text-green-500',
        bgColor: 'bg-green-100'
      };
    }
    
    return { 
      status: 'normal', 
      percentage: 0,
      color: 'text-blue-500',
      bgColor: 'bg-blue-100'
    };
  };
  
  const pricingStatus = getPricingStatus();
  const hourlyData = formatHourlyData(trafficData);
  const dailyData = formatDailyData(trafficData);
  const pieData = [
    { name: 'Occupied', value: trafficData?.active_tables || 0, color: '#22c55e' },
    { name: 'Available', value: (trafficData?.total_tables || 0) - (trafficData?.active_tables || 0), color: '#94a3b8' }
  ];
  
  // Custom colors for charts
  const COLORS = ['#22c55e', '#94a3b8', '#f59e0b', '#ef4444'];

  return (
    <AdminLayout
      title={t('analytics')}
      description={t('reviewAnalytics')}
    >
      <Tabs defaultValue="traffic" className="w-full" onValueChange={setActiveTab}>
        <div className="flex justify-between items-center mb-4">
          <TabsList>
            <TabsTrigger value="traffic">{t('trafficAnalytics')}</TabsTrigger>
            <TabsTrigger value="sales">{t('salesAnalytics')}</TabsTrigger>
            <TabsTrigger value="menu">{t('menuAnalytics')}</TabsTrigger>
            <TabsTrigger value="sentiment">Customer Sentiment</TabsTrigger>
          </TabsList>
          
          <Button variant="outline" size="sm" onClick={() => window.location.reload()}>
            {t('refreshData')}
          </Button>
        </div>
        
        <TabsContent value="traffic" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Current Traffic Stats */}
            <Card className="col-span-1">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">{t('currentTraffic')}</CardTitle>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <Skeleton className="h-12 w-full" />
                ) : (
                  <div className="flex flex-col">
                    <div className="flex items-end space-x-1">
                      <div className="text-3xl font-bold">
                        {trafficData ? Math.round(trafficData.current_occupancy * 100) : 0}%
                      </div>
                      <Badge variant="outline" className={`${pricingStatus.color} mb-1`}>
                        {t(pricingStatus.status)}
                      </Badge>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      <Clock className="h-3 w-3 inline-block mr-1" />
                      {t('lastUpdated')}: {trafficData?.last_updated ? new Date(trafficData.last_updated).toLocaleTimeString() : '-'}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
            
            {/* Table Occupancy */}
            <Card className="col-span-1">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">{t('tableOccupancy')}</CardTitle>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <Skeleton className="h-12 w-full" />
                ) : (
                  <div className="flex flex-col">
                    <div className="flex items-end space-x-1">
                      <div className="text-3xl font-bold">
                        {trafficData ? `${trafficData.active_tables}/${trafficData.total_tables}` : '0/0'}
                      </div>
                      <span className="text-muted-foreground text-sm mb-1">{t('tables')}</span>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      <Users className="h-3 w-3 inline-block mr-1" />
                      {trafficData ? Math.round((trafficData.active_tables / trafficData.total_tables) * 100) : 0}% {t('occupancy')}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
            
            {/* Dynamic Pricing Status */}
            <Card className="col-span-1">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">{t('pricingStatus')}</CardTitle>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <Skeleton className="h-12 w-full" />
                ) : (
                  <div className="flex flex-col">
                    <div className="flex items-end space-x-1">
                      <div className="text-3xl font-bold">
                        {pricingStatus.percentage > 0 ? '+' : ''}{pricingStatus.percentage}%
                      </div>
                      {pricingStatus.percentage !== 0 && (
                        <span className={`text-sm mb-1 ${pricingStatus.color}`}>
                          {pricingStatus.percentage > 0 ? <TrendingUp className="h-4 w-4 inline-block" /> : <TrendingDown className="h-4 w-4 inline-block" />}
                        </span>
                      )}
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      <DollarSign className="h-3 w-3 inline-block mr-1" />
                      {t('currentDynamicPricing')}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
            
            {/* Traffic Confidence */}
            <Card className="col-span-1">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">{t('dataConfidence')}</CardTitle>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <Skeleton className="h-12 w-full" />
                ) : (
                  <div className="flex flex-col">
                    <div className="flex items-end space-x-1">
                      <div className="text-3xl font-bold">
                        {trafficData ? Math.round(trafficData.confidence * 100) : 0}%
                      </div>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      {trafficData && trafficData.confidence >= 0.8 ? (
                        <span className="text-green-500">{t('highConfidence')}</span>
                      ) : trafficData && trafficData.confidence >= 0.5 ? (
                        <span className="text-amber-500">{t('mediumConfidence')}</span>
                      ) : (
                        <span className="text-red-500">{t('lowConfidence')}</span>
                      )}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Hourly Traffic Trend */}
            <Card className="col-span-2">
              <CardHeader>
                <CardTitle>{t('trafficTrend')}</CardTitle>
                <CardDescription>{t('todayTraffic')}</CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <Skeleton className="h-[300px] w-full" />
                ) : hourlyData.length > 0 ? (
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={hourlyData} margin={{ top: 5, right: 20, bottom: 5, left: 0 }}>
                      <Line type="monotone" dataKey="occupancy" stroke="#0ea5e9" strokeWidth={2} />
                      <XAxis dataKey="time" />
                      <YAxis domain={[0, 100]} unit="%" />
                      <Tooltip 
                        formatter={(value: number) => [`${value}%`, t('occupancy')]}
                        labelFormatter={(label) => `${t('time')}: ${label}`}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="h-[300px] flex items-center justify-center">
                    <p className="text-muted-foreground">{t('noTrafficData')}</p>
                  </div>
                )}
              </CardContent>
            </Card>
            
            {/* Table Occupancy Chart */}
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>{t('tableStatus')}</CardTitle>
                <CardDescription>{t('currentOccupancy')}</CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <Skeleton className="h-[300px] w-full" />
                ) : trafficData ? (
                  <div className="flex flex-col items-center">
                    <ResponsiveContainer width="100%" height={200}>
                      <PieChart>
                        <Pie
                          data={pieData}
                          cx="50%"
                          cy="50%"
                          innerRadius={60}
                          outerRadius={80}
                          paddingAngle={5}
                          dataKey="value"
                          label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                        >
                          {pieData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip formatter={(value: number) => [value, t('tables')]} />
                      </PieChart>
                    </ResponsiveContainer>
                    <div className="flex space-x-6 mt-4">
                      {pieData.map((entry, index) => (
                        <div key={index} className="flex items-center">
                          <div className="w-3 h-3 mr-2" style={{ backgroundColor: entry.color }} />
                          <span className="text-sm">{t(entry.name.toLowerCase())}: {entry.value}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="h-[300px] flex items-center justify-center">
                    <p className="text-muted-foreground">{t('noTableData')}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
          
          {/* Weekly Traffic Pattern */}
          <Card>
            <CardHeader>
              <CardTitle>{t('weeklyTrafficPattern')}</CardTitle>
              <CardDescription>{t('last7Days')}</CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <Skeleton className="h-[300px] w-full" />
              ) : dailyData.length > 0 ? (
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={dailyData} margin={{ top: 5, right: 20, bottom: 5, left: 0 }}>
                    <Bar dataKey="occupancy" fill="#0ea5e9" radius={[4, 4, 0, 0]} />
                    <XAxis dataKey="day" />
                    <YAxis domain={[0, 100]} unit="%" />
                    <Tooltip 
                      formatter={(value: number) => [`${value}%`, t('averageOccupancy')]}
                      labelFormatter={(label) => `${t('day')}: ${label}`}
                    />
                  </BarChart>
                </ResponsiveContainer>
              ) : (
                <div className="h-[300px] flex items-center justify-center">
                  <p className="text-muted-foreground">{t('noWeeklyData')}</p>
                </div>
              )}
            </CardContent>
          </Card>
          
          {/* Pricing Rules Summary */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div>
                <CardTitle>{t('pricingRules')}</CardTitle>
                <CardDescription>{t('currentDynamicPricingRules')}</CardDescription>
              </div>
              <Button variant="outline" size="sm" onClick={() => window.location.href = '/settings'}>
                {t('editRules')}
                <ChevronRight className="ml-1 h-4 w-4" />
              </Button>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <Skeleton className="h-24 w-full" />
              ) : pricingRules ? (
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                  <div className={`p-4 rounded-lg ${pricingStatus.status === 'high' ? pricingStatus.bgColor + ' border-2 border-' + pricingStatus.color : 'bg-gray-50'}`}>
                    <h4 className="font-semibold mb-1">{t('highTraffic')}</h4>
                    <p className="text-sm">≥ {(pricingRules.highTraffic?.threshold || 0.8) * 100}% {t('occupancy')}</p>
                    <p className="text-sm font-medium mt-1">{pricingRules.highTraffic?.percentage > 0 ? '+' : ''}{pricingRules.highTraffic?.percentage || 10}% {t('priceAdjustment')}</p>
                  </div>
                  
                  <div className={`p-4 rounded-lg ${pricingStatus.status === 'medium' ? pricingStatus.bgColor + ' border-2 border-' + pricingStatus.color : 'bg-gray-50'}`}>
                    <h4 className="font-semibold mb-1">{t('mediumTraffic')}</h4>
                    <p className="text-sm">≥ {(pricingRules.mediumTraffic?.threshold || 0.5) * 100}% {t('occupancy')}</p>
                    <p className="text-sm font-medium mt-1">{pricingRules.mediumTraffic?.percentage > 0 ? '+' : ''}{pricingRules.mediumTraffic?.percentage || 5}% {t('priceAdjustment')}</p>
                  </div>
                  
                  <div className={`p-4 rounded-lg ${pricingStatus.status === 'low' ? pricingStatus.bgColor + ' border-2 border-' + pricingStatus.color : 'bg-gray-50'}`}>
                    <h4 className="font-semibold mb-1">{t('lowTraffic')}</h4>
                    <p className="text-sm">≤ {(pricingRules.lowTraffic?.threshold || 0.3) * 100}% {t('occupancy')}</p>
                    <p className="text-sm font-medium mt-1">{pricingRules.lowTraffic?.percentage > 0 ? '+' : ''}{pricingRules.lowTraffic?.percentage || -5}% {t('priceAdjustment')}</p>
                  </div>
                </div>
              ) : (
                <p className="text-muted-foreground">{t('noPricingRules')}</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="sales" className="space-y-4">
          {/* Sales Overview Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">{t('totalSales')}</CardTitle>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <Skeleton className="h-12 w-full" />
                ) : salesData ? (
                  <div className="flex items-center">
                    <DollarSign className="h-5 w-5 text-restaurant-primary mr-2" />
                    <div>
                      <p className="text-2xl font-bold">
                        €{salesData.total_sales.toFixed(2)}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {timeRange === 'day' ? t('last24Hours') : timeRange === 'week' ? t('last7Days') : t('last30Days')}
                      </p>
                    </div>
                  </div>
                ) : (
                  <p className="text-muted-foreground">{t('noSalesData')}</p>
                )}
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">{t('orderCount')}</CardTitle>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <Skeleton className="h-12 w-full" />
                ) : salesData ? (
                  <div className="flex items-center">
                    <Utensils className="h-5 w-5 text-restaurant-primary mr-2" />
                    <div>
                      <p className="text-2xl font-bold">{salesData.order_count}</p>
                      <p className="text-xs text-muted-foreground">
                        {timeRange === 'day' ? t('last24Hours') : timeRange === 'week' ? t('last7Days') : t('last30Days')}
                      </p>
                    </div>
                  </div>
                ) : (
                  <p className="text-muted-foreground">{t('noSalesData')}</p>
                )}
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">{t('averageOrderValue')}</CardTitle>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <Skeleton className="h-12 w-full" />
                ) : salesData ? (
                  <div className="flex items-center">
                    <Users className="h-5 w-5 text-restaurant-primary mr-2" />
                    <div>
                      <p className="text-2xl font-bold">
                        €{salesData.average_order_value.toFixed(2)}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {timeRange === 'day' ? t('last24Hours') : timeRange === 'week' ? t('last7Days') : t('last30Days')}
                      </p>
                    </div>
                  </div>
                ) : (
                  <p className="text-muted-foreground">{t('noSalesData')}</p>
                )}
              </CardContent>
            </Card>
          </div>
          
          {/* Sales Charts */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>{t('salesByHour')}</CardTitle>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <Skeleton className="h-[300px] w-full" />
                ) : salesData && salesData.sales_by_hour.some(h => h.sales > 0) ? (
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={salesData.sales_by_hour.filter(h => h.sales > 0)} margin={{ top: 5, right: 20, bottom: 5, left: 0 }}>
                      <Bar dataKey="sales" fill="#0ea5e9" radius={[4, 4, 0, 0]} />
                      <XAxis dataKey="hour" tickFormatter={(hour) => `${hour}:00`} />
                      <YAxis />
                      <Tooltip 
                        formatter={(value: number) => [`€${value.toFixed(2)}`, t('sales')]}
                        labelFormatter={(hour) => `${hour}:00`}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="h-[300px] flex items-center justify-center">
                    <p className="text-muted-foreground">{t('noSalesData')}</p>
                  </div>
                )}
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>{t('salesByCategory')}</CardTitle>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <Skeleton className="h-[300px] w-full" />
                ) : salesData && salesData.sales_by_category.length > 0 ? (
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={salesData.sales_by_category}
                        cx="50%"
                        cy="50%"
                        innerRadius={60}
                        outerRadius={80}
                        paddingAngle={5}
                        dataKey="sales"
                        nameKey="category"
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      >
                        {salesData.sales_by_category.map((_, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value: number) => [`€${value.toFixed(2)}`, t('sales')]} />
                    </PieChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="h-[300px] flex items-center justify-center">
                    <p className="text-muted-foreground">{t('noSalesData')}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
          
          {/* Sales by Day of Week */}
          <Card>
            <CardHeader>
              <CardTitle>{t('salesByDayOfWeek')}</CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <Skeleton className="h-[300px] w-full" />
              ) : salesData && salesData.sales_by_day.some(d => d.sales > 0) ? (
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={salesData.sales_by_day} margin={{ top: 5, right: 20, bottom: 5, left: 0 }}>
                    <Bar dataKey="sales" fill="#0ea5e9" radius={[4, 4, 0, 0]} />
                    <XAxis dataKey="day" />
                    <YAxis />
                    <Tooltip 
                      formatter={(value: number) => [`$${value.toFixed(2)}`, t('sales')]}
                    />
                  </BarChart>
                </ResponsiveContainer>
              ) : (
                <div className="h-[300px] flex items-center justify-center">
                  <p className="text-muted-foreground">{t('noSalesData')}</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="menu" className="space-y-4">
          {/* Time Range Selector */}
          <div className="flex justify-end mb-4">
            <div className="flex space-x-2">
              <Button 
                variant={timeRange === 'day' ? "default" : "outline"} 
                size="sm"
                onClick={() => {
                  console.log('Changing time range to day');
                  setTimeRange('day');
                  // This will trigger the useEffect since timeRange dependency changed
                }}
              >
                {t('day')}
              </Button>
              <Button 
                variant={timeRange === 'week' ? "default" : "outline"} 
                size="sm"
                onClick={() => {
                  console.log('Changing time range to week');
                  setTimeRange('week');
                }}
              >
                {t('week')}
              </Button>
              <Button 
                variant={timeRange === 'month' ? "default" : "outline"} 
                size="sm"
                onClick={() => {
                  console.log('Changing time range to month');
                  setTimeRange('month');
                }}
              >
                {t('month')}
              </Button>
            </div>
          </div>
          
          {/* Popular Items */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div>
                <CardTitle>{t('popularItems')}</CardTitle>
                <CardDescription>{t('bestSellingItems')}</CardDescription>
              </div>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <Skeleton className="h-[400px] w-full" />
              ) : menuData && menuData.length > 0 ? (
                <div className="space-y-4">
                  {/* Display help text to show current time filter */}
                  <p className="text-xs text-muted-foreground mb-2">
                    {timeRange === 'day' && t('last24Hours')}
                    {timeRange === 'week' && t('last7Days')}
                    {timeRange === 'month' && t('last30Days')}
                  </p>
                  {/* Debug info - check what menu data we have */}
                  <div className="hidden">{JSON.stringify(menuData)}</div>
                  
                  {menuData
                    // Sorted by orders count for the specified time period
                    .sort((a, b) => {
                      // Use the correct order count field based on time range
                      const aCount = timeRange === 'day' ? a.day_orders || 0 : 
                                   timeRange === 'week' ? a.week_orders || 0 : a.month_orders || 0;
                      const bCount = timeRange === 'day' ? b.day_orders || 0 : 
                                   timeRange === 'week' ? b.week_orders || 0 : b.month_orders || 0;
                      return bCount - aCount;
                    })
                    .slice(0, 5)
                    .map((item, index) => {
                      // Get the correct orders count based on time range
                      const ordersCount = timeRange === 'day' ? item.day_orders || 0 : 
                                        timeRange === 'week' ? item.week_orders || 0 : item.month_orders || 0;
                      
                      // Show all menu items even with zero orders to ensure we see something
                      // This helps display menu items when you haven't had any orders yet
                      
                      return (
                        <div key={item.id} className="flex items-center justify-between">
                          <div className="flex items-center">
                            <div className="flex h-9 w-9 items-center justify-center rounded-full bg-restaurant-primary/10 text-restaurant-primary">
                              {index + 1}
                            </div>
                            <div className="ml-4">
                              <p className="text-sm font-medium">{item.name}</p>
                              <p className="text-xs text-muted-foreground">{item.category}</p>
                            </div>
                          </div>
                          <div className="flex items-center">
                            <p className="font-medium">€{item.current_price?.toFixed(2) || item.base_price?.toFixed(2) || '0.00'}</p>
                            <Badge className="ml-2" variant="outline">{ordersCount} {t('orders')}</Badge>
                          </div>
                        </div>
                      );
                    }).filter(Boolean) /* Remove null items */
                  }
                  {/* Don't show a message - we're already showing the menu items with 0 orders */}
                </div>
              ) : (
                <div className="h-[300px] flex items-center justify-center">
                  <p className="text-muted-foreground">{t('noPopularItems')}</p>
                </div>
              )}
            </CardContent>
          </Card>
          
          {/* Price Recommendations */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div>
                <CardTitle>{t('priceRecommendations')}</CardTitle>
                <CardDescription>{t('smartPricingSuggestions')}</CardDescription>
              </div>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <Skeleton className="h-[300px] w-full" />
              ) : priceRecommendations && priceRecommendations.length > 0 ? (
                <div className="space-y-4">
                  {priceRecommendations.slice(0, 5).map((rec, index) => (
                    <div key={rec.itemId} className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className={`flex h-9 w-9 items-center justify-center rounded-full ${rec.adjustment > 0 ? 'bg-green-100 text-green-600' : 'bg-blue-100 text-blue-600'}`}>
                          {rec.adjustment > 0 ? <TrendingUp className="h-5 w-5" /> : <TrendingDown className="h-5 w-5" />}
                        </div>
                        <div className="ml-4">
                          <p className="text-sm font-medium">{rec.name}</p>
                          <p className="text-xs text-muted-foreground">{rec.reason}</p>
                        </div>
                      </div>
                      <div>
                        <div className="flex items-center space-x-2">
                          <p className="text-sm text-muted-foreground line-through">€{rec.currentPrice.toFixed(2)}</p>
                          <p className="text-sm font-medium">€{rec.suggestedPrice.toFixed(2)}</p>
                          <Badge className={rec.adjustment > 0 ? 'bg-green-100 text-green-600' : 'bg-blue-100 text-blue-600'}>
                            {rec.adjustment > 0 ? '+' : ''}{rec.adjustment}%
                          </Badge>
                        </div>
                      </div>
                    </div>
                  ))}
                  <div className="pt-2 text-center">
                    <Button variant="ghost" size="sm" onClick={() => window.location.href = '/settings'}>
                      {t('applyToMenu')}
                      <ChevronRight className="ml-1 h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="h-[300px] flex items-center justify-center">
                  <p className="text-muted-foreground">{t('noPricingRecommendations')}</p>
                </div>
              )}
            </CardContent>
          </Card>
          
          {/* Category Performance */}
          <Card>
            <CardHeader>
              <CardTitle>{t('categoryPerformance')}</CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <Skeleton className="h-[300px] w-full" />
              ) : salesData && salesData.sales_by_category.length > 0 ? (
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={salesData.sales_by_category.sort((a, b) => b.sales - a.sales)} margin={{ top: 5, right: 20, bottom: 5, left: 0 }}>
                    <Bar dataKey="sales" fill="#0ea5e9" radius={[4, 4, 0, 0]} />
                    <XAxis dataKey="category" />
                    <YAxis />
                    <Tooltip 
                      formatter={(value: number) => [`$${value.toFixed(2)}`, t('sales')]}
                    />
                  </BarChart>
                </ResponsiveContainer>
              ) : (
                <div className="h-[300px] flex items-center justify-center">
                  <p className="text-muted-foreground">{t('noCategoryData')}</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="sentiment" className="space-y-4">
          <CustomerSentimentAnalytics
            restaurantId={user?.user_metadata?.restaurant_id}
            timeRange={timeRange}
          />
        </TabsContent>
      </Tabs>
    </AdminLayout>
  );
};

export default Analytics;
