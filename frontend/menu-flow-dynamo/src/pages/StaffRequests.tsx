import React, { useEffect, useState } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { MessageSquare, Users, Utensils, HelpCircle, Clock, CheckCircle, X } from 'lucide-react';
import { toast } from 'sonner';
import AdminLayout from '@/components/AdminLayout';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';

interface StaffRequest {
  id: string;
  restaurant_id: string;
  table_id?: string;
  table_number?: string;
  request_type: string;
  message: string;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  created_at: string;
  updated_at: string;
}

const StaffRequests = () => {
  const { user } = useAuth();
  const [requests, setRequests] = useState<StaffRequest[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const requestTypeIcons = {
    assistance: HelpCircle,
    service: Users,
    order: Utensils,
    other: MessageSquare
  };

  const requestTypeLabels = {
    assistance: 'General Assistance',
    service: 'Table Service',
    order: 'Order Issue',
    other: 'Other'
  };

  const statusColors = {
    pending: 'bg-yellow-100 text-yellow-800',
    in_progress: 'bg-blue-100 text-blue-800',
    completed: 'bg-green-100 text-green-800',
    cancelled: 'bg-gray-100 text-gray-800'
  };

  useEffect(() => {
    fetchStaffRequests();
    
    // Set up real-time subscription for new requests
    const subscription = supabase
      .channel('staff_requests')
      .on('postgres_changes', 
        { 
          event: '*', 
          schema: 'public', 
          table: 'staff_requests',
          filter: `restaurant_id=eq.${user?.user_metadata?.restaurant_id}`
        }, 
        (payload) => {
          console.log('Staff request change:', payload);
          fetchStaffRequests(); // Refresh the list
          
          if (payload.eventType === 'INSERT') {
            toast.info('New staff request received!');
          }
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [user]);

  const fetchStaffRequests = async () => {
    setIsLoading(true);
    try {
      const restaurantId = user?.user_metadata?.restaurant_id;
      if (!restaurantId) {
        console.warn('No restaurant ID available');
        setIsLoading(false);
        return;
      }

      // Try to fetch from staff_requests table first
      let { data: staffRequestsData, error: staffRequestsError } = await supabase
        .from('staff_requests')
        .select(`
          *,
          restaurant_tables!left(table_number)
        `)
        .eq('restaurant_id', restaurantId)
        .order('created_at', { ascending: false });

      if (staffRequestsError && staffRequestsError.code !== '42P01') {
        console.error('Error fetching staff requests:', staffRequestsError);
      }

      // If staff_requests table doesn't exist or is empty, try notifications
      if (!staffRequestsData || staffRequestsData.length === 0) {
        const { data: notificationsData, error: notificationsError } = await supabase
          .from('notifications')
          .select('*')
          .eq('restaurant_id', restaurantId)
          .eq('type', 'staff_request')
          .order('created_at', { ascending: false });

        if (notificationsError) {
          console.error('Error fetching notifications:', notificationsError);
          setIsLoading(false);
          return;
        }

        // Convert notifications to staff request format
        const convertedRequests = notificationsData?.map(notification => ({
          id: notification.id,
          restaurant_id: notification.restaurant_id,
          table_id: notification.metadata?.table_id,
          table_number: notification.metadata?.table_number || 'Unknown',
          request_type: notification.metadata?.request_type || 'other',
          message: notification.metadata?.customer_message || notification.message,
          status: notification.read ? 'completed' : 'pending',
          created_at: notification.created_at,
          updated_at: notification.updated_at || notification.created_at
        })) || [];

        setRequests(convertedRequests);
      } else {
        // Use staff_requests data
        const formattedRequests = staffRequestsData.map(request => ({
          ...request,
          table_number: request.restaurant_tables?.table_number || 'Unknown'
        }));
        setRequests(formattedRequests);
      }
    } catch (error) {
      console.error('Error fetching staff requests:', error);
      toast.error('Failed to load staff requests');
    } finally {
      setIsLoading(false);
    }
  };

  const updateRequestStatus = async (requestId: string, newStatus: string) => {
    try {
      // Try to update in staff_requests table first
      const { error: staffRequestsError } = await supabase
        .from('staff_requests')
        .update({ 
          status: newStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', requestId);

      if (staffRequestsError && staffRequestsError.code !== '42P01') {
        console.error('Error updating staff request:', staffRequestsError);
        return;
      }

      // If staff_requests table doesn't exist, update notifications
      if (staffRequestsError?.code === '42P01') {
        await supabase
          .from('notifications')
          .update({ 
            read: newStatus === 'completed',
            updated_at: new Date().toISOString()
          })
          .eq('id', requestId);
      }

      // Update local state
      setRequests(prev => prev.map(request => 
        request.id === requestId 
          ? { ...request, status: newStatus as any, updated_at: new Date().toISOString() }
          : request
      ));

      toast.success(`Request marked as ${newStatus}`);
    } catch (error) {
      console.error('Error updating request status:', error);
      toast.error('Failed to update request status');
    }
  };

  if (isLoading) {
    return (
      <AdminLayout title="Staff Requests" description="Manage customer assistance requests">
        <div className="space-y-4">
          {[1, 2, 3].map(i => (
            <Skeleton key={i} className="h-32 w-full" />
          ))}
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout title="Staff Requests" description="Manage customer assistance requests">
      <div className="space-y-4">
        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <Clock className="h-5 w-5 text-yellow-500 mr-2" />
                <div>
                  <p className="text-2xl font-bold">
                    {requests.filter(r => r.status === 'pending').length}
                  </p>
                  <p className="text-xs text-muted-foreground">Pending</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <Users className="h-5 w-5 text-blue-500 mr-2" />
                <div>
                  <p className="text-2xl font-bold">
                    {requests.filter(r => r.status === 'in_progress').length}
                  </p>
                  <p className="text-xs text-muted-foreground">In Progress</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                <div>
                  <p className="text-2xl font-bold">
                    {requests.filter(r => r.status === 'completed').length}
                  </p>
                  <p className="text-xs text-muted-foreground">Completed</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <MessageSquare className="h-5 w-5 text-purple-500 mr-2" />
                <div>
                  <p className="text-2xl font-bold">{requests.length}</p>
                  <p className="text-xs text-muted-foreground">Total Requests</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Requests List */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Staff Requests</CardTitle>
          </CardHeader>
          <CardContent>
            {requests.length > 0 ? (
              <div className="space-y-4">
                {requests.map((request) => {
                  const IconComponent = requestTypeIcons[request.request_type as keyof typeof requestTypeIcons] || MessageSquare;
                  return (
                    <div key={request.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <IconComponent className="h-5 w-5 text-gray-600" />
                          <span className="font-medium">
                            Table {request.table_number}
                          </span>
                          <Badge variant="outline">
                            {requestTypeLabels[request.request_type as keyof typeof requestTypeLabels] || 'Other'}
                          </Badge>
                          <Badge className={statusColors[request.status]}>
                            {request.status.replace('_', ' ')}
                          </Badge>
                        </div>
                        <span className="text-xs text-muted-foreground">
                          {new Date(request.created_at).toLocaleString()}
                        </span>
                      </div>
                      
                      {request.message && (
                        <p className="text-sm text-muted-foreground mb-3">
                          "{request.message}"
                        </p>
                      )}
                      
                      <div className="flex space-x-2">
                        {request.status === 'pending' && (
                          <>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => updateRequestStatus(request.id, 'in_progress')}
                            >
                              Start Working
                            </Button>
                            <Button
                              size="sm"
                              onClick={() => updateRequestStatus(request.id, 'completed')}
                            >
                              Mark Complete
                            </Button>
                          </>
                        )}
                        
                        {request.status === 'in_progress' && (
                          <Button
                            size="sm"
                            onClick={() => updateRequestStatus(request.id, 'completed')}
                          >
                            Mark Complete
                          </Button>
                        )}
                        
                        {request.status !== 'cancelled' && request.status !== 'completed' && (
                          <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => updateRequestStatus(request.id, 'cancelled')}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className="text-center py-8">
                <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-muted-foreground">No staff requests at the moment</p>
                <p className="text-sm text-muted-foreground">
                  Customer requests will appear here when they need assistance
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
};

export default StaffRequests;
