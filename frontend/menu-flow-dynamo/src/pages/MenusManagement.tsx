import React, { useState } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { Plus, Edit, Trash, Link } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from '@/components/ui/table';
import { useToast } from '@/components/ui/use-toast';
import { useLanguage } from '@/contexts/LanguageContext';
import AdminLayout from '@/components/AdminLayout';
import MenuForm from '@/components/MenuForm';
import MenuTableAssignments from '@/components/MenuTableAssignments';
import { fetchMenus as fetchMenusFromDb, deleteMenu } from '@/services/menuDbService';
import { useAuth } from '@/contexts/AuthContext';
import { useRestaurant } from '@/contexts/RestaurantContext';
import { RestaurantContextGuard } from '@/components/RestaurantContextGuard';

// Import the Menu type from menuDbService to ensure consistency
import { Menu } from '@/services/menuDbService';

const fetchMenus = async (restaurantInfo: { id: string; name: string }) => {
  try {
    // Skip fetching if restaurant info is not ready
    if (!restaurantInfo || !restaurantInfo.id) {
      console.warn('Skipping fetchMenus: restaurantInfo is not ready');
      return [];
    }
    
    // Get menus from database, passing the restaurant info to ensure proper filtering
    const menus = await fetchMenusFromDb(restaurantInfo);
    return menus;
  } catch (error) {
    console.error('Error fetching menus from database:', error);
    return [];
  }
};

const MenusManagement = () => {
  const { t } = useLanguage();
  const { toast } = useToast();
  const { user } = useAuth();
  const { restaurantInfo, isLoading: isRestaurantLoading } = useRestaurant();
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isAssignmentOpen, setIsAssignmentOpen] = useState(false);
  const [selectedMenu, setSelectedMenu] = useState<Menu | null>(null);
  const queryClient = useQueryClient();

  // Use React Query with proper dependency on restaurant context
  const { data: menus, isLoading, isError, error, refetch } = useQuery({
    queryKey: ['menus', restaurantInfo?.id],
    queryFn: () => fetchMenus(restaurantInfo),
    enabled: !!user && !!restaurantInfo?.id, // Only run when both user and restaurant ID are available
    staleTime: 0, // Always consider data stale to ensure fresh fetches
    refetchOnMount: true, // Always refetch when component mounts
  });

  // Enhanced refetch function that also invalidates the query cache
  const handleRefetch = async () => {
    console.log('Refreshing menus data...');
    try {
      // Invalidate the query cache first
      await queryClient.invalidateQueries({ queryKey: ['menus', restaurantInfo?.id] });
      // Then refetch
      await refetch();
      console.log('Menus data refreshed successfully');
    } catch (error) {
      console.error('Error refreshing menus:', error);
    }
  };

  const handleAddMenu = () => {
    setSelectedMenu(null);
    setIsFormOpen(true);
  };

  const handleEditMenu = (menu: Menu) => {
    setSelectedMenu(menu);
    setIsFormOpen(true);
  };

  const handleAssignTables = (menu: Menu) => {
    setSelectedMenu(menu);
    setIsAssignmentOpen(true);
  };

  const handleDeleteMenu = async (menuId: string) => {
    try {
      await deleteMenu(menuId);
      toast({
        title: t('menu.deleted'),
        description: t('menu.deleteSuccess'),
        variant: 'default', // Using 'default' instead of 'success' to match available variants
      });
      await handleRefetch();
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : t('menu.deleteError');
      toast({
        title: t('menu.deleteFailed'),
        description: errorMessage,
        variant: 'destructive',
      });
    }
  };

  return (
    <RestaurantContextGuard>
      <AdminLayout
        title="Menus Management"
        description="Manage your restaurant's menus"
      >
        <div className="mb-6 flex justify-between items-center">
          <h2 className="text-xl font-semibold">Menus</h2>
          <Button onClick={handleAddMenu}>
            <Plus className="h-4 w-4 mr-2" /> Add Menu
          </Button>
        </div>

        {/* Menu Form Dialog */}
        <MenuForm
          isOpen={isFormOpen}
          onClose={() => setIsFormOpen(false)}
          onSuccess={handleRefetch}
          editingMenu={selectedMenu}
        />

        {/* Menu Table Assignments Dialog */}
        <MenuTableAssignments
          isOpen={isAssignmentOpen}
          onClose={() => setIsAssignmentOpen(false)}
          menuId={selectedMenu?.id}
        />

        <Card className="overflow-hidden">
          {isLoading || isRestaurantLoading ? (
            <div className="p-8 text-center">
              <div className="inline-block h-6 w-6 animate-spin rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em] text-sky-500"></div>
              <p className="mt-2 text-gray-600">Loading menus...</p>
            </div>
          ) : isError ? (
            <div className="p-8 text-center text-red-500">
              Error loading menus: {error instanceof Error ? error.message : 'Unknown error'}
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Availability</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {menus && menus.length > 0 ? (
                  menus.map((menu) => (
                    <TableRow key={menu.id}>
                      <TableCell className="font-medium">{menu.name}</TableCell>
                      <TableCell>{menu.description || '-'}</TableCell>
                      <TableCell>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          menu.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                        }`}>
                          {menu.is_active ? 'Active' : 'Inactive'}
                        </span>
                      </TableCell>
                      <TableCell>
                        {menu.start_time && menu.end_time
                          ? `${menu.start_time} - ${menu.end_time}`
                          : 'All day'}
                      </TableCell>
                      <TableCell className="text-right space-x-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleAssignTables(menu)}
                          title="Assign to tables"
                        >
                          <Link className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleEditMenu(menu)}
                          title="Edit menu"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="text-red-500"
                          onClick={() => handleDeleteMenu(menu.id)}
                          title="Delete menu"
                        >
                          <Trash className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-6">
                      No menus found. Click "Add Menu" to create one.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          )}
        </Card>
      </AdminLayout>
    </RestaurantContextGuard>
  );
};

export default MenusManagement;
