
import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import Header from '@/components/Header';
import MenuSection from '@/components/MenuSection';
import OrderCart from '@/components/OrderCart';
import { MenuItemType } from '@/components/MenuItem';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from 'sonner';
import { fetchMenuItems } from '@/services/menuService';
import { useRestaurant } from '@/contexts/RestaurantContext';
import { Loader2, QrCode } from 'lucide-react';
import { getCurrentTableId, storeTableSession, getTableSession, clearTableSession } from '@/services/tableSessionService';
import { recordTrafficEvent } from '@/services/trafficService';
import { getRestaurantId } from '@/services/restaurantDbService';
import StaffRequestButton from '@/components/StaffRequestButton';

interface CartItem extends MenuItemType {
  quantity: number;
}

const Menu = () => {
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [isCartOpen, setIsCartOpen] = useState(false);
  const [menuItems, setMenuItems] = useState<MenuItemType[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { restaurantInfo } = useRestaurant();
  // Record menu view event for traffic monitoring
  useEffect(() => {
    if (restaurantInfo?.id) {
      recordTrafficEvent('menu_view', restaurantInfo.id);
    }
  }, [restaurantInfo?.id]);
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  // Function to load menu items (wrapped in useCallback to prevent unnecessary re-renders)
  const loadMenuItems = useCallback(async () => {
    try {
      setIsLoading(true);
      
      // First check URL for table ID
      let tableId = searchParams.get('table');
      
      // If no table ID in URL, try to get it from stored session (only if session is valid)
      if (!tableId) {
        // This will automatically check if the session is still valid (within 90 min)
        const session = getTableSession();
        if (session && session.isValid) {
          tableId = session.tableId;
          console.log('📋 Using stored table ID:', tableId, '(session valid for 90 minutes)');
        } else if (session && !session.isValid) {
          // Session expired - clear it
          clearTableSession();
          console.log('⏰ Table session expired');
          toast.info('Please scan the QR code at your table again - your previous session has expired.');
        }
      } else {
        // If we have a table ID in URL, check if it's different from the current one
        const currentSession = getTableSession(false); // Don't check validity here
        const restaurantId = await getRestaurantId();
        
        if (restaurantId) {
          // If we have a different table ID, clear order history from the previous table
          if (currentSession && currentSession.tableId !== tableId) {
            console.log('🔄 Table changed from', currentSession.tableId, 'to', tableId);
            // Clear local storage order data to avoid showing previous table's orders
            localStorage.removeItem('menu_flow_order_history');
            toast.info('You have moved to a new table!');
          }
          
        // Store the new table session
        storeTableSession(tableId, restaurantId);
        console.log('💾 Saved table ID to session:', tableId);
        // Record QR scan event for traffic monitoring
        try {
          await recordTrafficEvent('qr_scan', restaurantId);
          console.log('📡 Recorded QR scan event');
        } catch (e) {
          console.error('Error recording QR scan event:', e);
        }
        }
      }
      
      console.log('🔍 Loading menu items for table ID:', tableId);
      const items = await fetchMenuItems(tableId || undefined);
      setMenuItems(items);
      console.log('💲 Menu items loaded with dynamic pricing:', items.length);
      
      // Log items with price differences
      const itemsWithPriceChanges = items.filter(item => 
        item.base_price && item.price && item.base_price !== item.price
      );
      
      console.log(`📈 Found ${itemsWithPriceChanges.length} items with dynamic prices`);
      
      if (itemsWithPriceChanges.length > 0) {
        console.log('📈 Price changes detected in the menu:');
        itemsWithPriceChanges.forEach(item => {
          const priceDiff = ((item.price! / item.base_price!) - 1) * 100;
          console.log(`🏷️ ${item.name}: €${item.base_price!.toFixed(2)} → €${item.price!.toFixed(2)} (${priceDiff > 0 ? '+' : ''}${priceDiff.toFixed(1)}%)`);
        });
      }
    } catch (error) {
      console.error('Error loading menu items:', error);
      toast.error('Failed to load menu items. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  }, [searchParams, setMenuItems, setIsLoading]);

  useEffect(() => {
    loadMenuItems();
  }, [loadMenuItems, searchParams]);
  
  useEffect(() => {
    // Handler for dynamic pricing updates
    const handleDynamicPricingUpdate = (event: CustomEvent) => {
      console.log('🔔 Received dynamic pricing update event:', event.detail);
      toast.info(`Prices updated! (${event.detail.itemCount} items affected)`);
      loadMenuItems(); // Reload menu items to get the new prices
    };
    
    // Handler for traffic level changes
    const handleTrafficChange = (event: CustomEvent) => {
      console.log('🚌 Received traffic level change event:', event.detail);
    };
    
    // Handler for legacy traffic events
    const handleLegacyTrafficChange = () => {
      console.log('🔄 Received legacy traffic change event - refreshing menu items');
      loadMenuItems();
    };
    
    // Add all event listeners
    window.addEventListener('dynamic-pricing-updated', handleDynamicPricingUpdate as EventListener);
    window.addEventListener('traffic-level-changed', handleTrafficChange as EventListener);
    window.addEventListener('dynamic-pricing-traffic-change', handleLegacyTrafficChange);
    
    // Clean up event listeners on unmount
    return () => {
      window.removeEventListener('dynamic-pricing-updated', handleDynamicPricingUpdate as EventListener);
      window.removeEventListener('traffic-level-changed', handleTrafficChange as EventListener);
      window.removeEventListener('dynamic-pricing-traffic-change', handleLegacyTrafficChange);
    };
  }, [loadMenuItems]); // loadMenuItems is explicitly included as a dependency

  // Group items by category: treat any category with 'drink' or 'beverage' as drinks
  const drinksItems = menuItems.filter(item => {
    const cat = item.category?.toLowerCase() || '';
    return cat.includes('drink') || cat.includes('beverage');
  });
  // Treat other categories as food
  const foodItems = menuItems.filter(item => {
    const cat = item.category?.toLowerCase() || '';
    return !cat.includes('drink') && !cat.includes('beverage');
  });

  const addToCart = (item: MenuItemType) => {
    setCartItems(prevItems => {
      const existingItem = prevItems.find(i => i.id === item.id);

      if (existingItem) {
        return prevItems.map(i =>
          i.id === item.id ? { ...i, quantity: i.quantity + 1 } : i
        );
      } else {
        toast.success(`Added ${item.name} to your order`);
        return [...prevItems, { ...item, quantity: 1 }];
      }
    });
  };

  const increaseQuantity = (itemId: string) => {
    setCartItems(prevItems =>
      prevItems.map(item =>
        item.id === itemId ? { ...item, quantity: item.quantity + 1 } : item
      )
    );
  };

  const decreaseQuantity = (itemId: string) => {
    setCartItems(prevItems =>
      prevItems.map(item =>
        item.id === itemId && item.quantity > 1
          ? { ...item, quantity: item.quantity - 1 }
          : item
      )
    );
  };

  const removeItem = (itemId: string) => {
    setCartItems(prevItems => prevItems.filter(item => item.id !== itemId));
  };

  const handleCheckout = () => {
    // Store cart in session storage to access on confirmation page
    sessionStorage.setItem('cartItems', JSON.stringify(cartItems));
    navigate('/order-confirmation');
  };

  const totalItemCount = cartItems.reduce((sum, item) => sum + item.quantity, 0);

  return (
    <div className="min-h-screen flex flex-col bg-restaurant-background">
      <Header
        title={`${restaurantInfo.name} Menu`}
        showCart={true}
        cartItemCount={totalItemCount}
        onCartClick={() => setIsCartOpen(true)}
        showBackButton={true}
      />

      <main className="flex-1 container mx-auto px-4 py-6">
        <div className="max-w-2xl mx-auto">
          {isLoading ? (
            <div className="py-20 flex flex-col items-center justify-center">
              <Loader2 className="h-12 w-12 text-restaurant-primary animate-spin mb-4" />
              <p className="text-restaurant-text text-lg">Loading menu items...</p>
            </div>
          ) : (
            <Tabs defaultValue="all" className="mb-6">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="all">All Menu</TabsTrigger>
                <TabsTrigger value="drinks">Drinks</TabsTrigger>
                <TabsTrigger value="food">Food</TabsTrigger>
              </TabsList>

              <TabsContent value="all">
                {menuItems.length > 0 ? (
                  <>
                    {drinksItems.length > 0 && (
                      <MenuSection title="Drinks" items={drinksItems} onAddToCart={addToCart} />
                    )}
                    {foodItems.length > 0 && (
                      <MenuSection title="Food" items={foodItems} onAddToCart={addToCart} />
                    )}
                  </>
                ) : (
                  <div className="py-12 text-center">
                    <h3 className="text-xl font-medium mb-2">No menu items available</h3>
                    <p className="text-restaurant-muted">Please check back later or contact the restaurant.</p>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="drinks">
                {drinksItems.length > 0 ? (
                  <MenuSection title="Drinks" items={drinksItems} onAddToCart={addToCart} />
                ) : (
                  <div className="py-12 text-center">
                    <h3 className="text-xl font-medium mb-2">No drinks available</h3>
                    <p className="text-restaurant-muted">Please check back later or contact the restaurant.</p>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="food">
                {foodItems.length > 0 ? (
                  <MenuSection title="Food" items={foodItems} onAddToCart={addToCart} />
                ) : (
                  <div className="py-12 text-center">
                    <h3 className="text-xl font-medium mb-2">No food items available</h3>
                    <p className="text-restaurant-muted">Please check back later or contact the restaurant.</p>
                  </div>
                )}
              </TabsContent>
            </Tabs>
          )}

          {/* Staff Request Button */}
          <div className="mt-6 flex justify-center">
            <StaffRequestButton
              tableId={getCurrentTableId()}
              restaurantId={restaurantInfo?.id || ''}
              className="w-full max-w-xs"
            />
          </div>
        </div>
      </main>

      <OrderCart
        items={cartItems}
        onIncreaseQuantity={increaseQuantity}
        onDecreaseQuantity={decreaseQuantity}
        onRemoveItem={removeItem}
        onCheckout={handleCheckout}
        isOpen={isCartOpen}
        onClose={() => setIsCartOpen(false)}
      />
    </div>
  );
};

export default Menu;
