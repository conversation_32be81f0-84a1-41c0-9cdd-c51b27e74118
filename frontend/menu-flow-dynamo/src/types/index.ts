/**
 * Common type definitions for the Menu Flow Dynamo application
 */

export type OrderStatus = 'new' | 'pending' | 'preparing' | 'ready' | 'delivered' | 'completed' | 'cancelled';

export interface OrderItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  options?: {
    name: string;
    choice: string;
    price?: number;
  }[];
  notes?: string;
  variantId?: string;
}

export interface Order {
  id: string;
  table_id: string;
  restaurant_id: string;
  status: OrderStatus;
  items: OrderItem[];
  total_amount: number;
  created_at: string;
  updated_at?: string;
  customer_name?: string;
  customer_email?: string;
  notes?: string;
  payment_status?: 'pending' | 'paid' | 'failed';
  payment_method?: 'cash' | 'card' | 'mobile';
  orderNumber?: string; // Add optional order number field
}

export interface Restaurant {
  id: string;
  name: string;
  logo_url?: string;
  address?: string;
  phone?: string;
  email?: string;
  currency: string;
  locale: string;
  timezone: string;
  updated_at: string;
  created_at: string;
  is_active: boolean;
  user_id: string;
}

export interface RestaurantTable {
  id: string;
  restaurant_id: string;
  table_number: number;
  status: 'available' | 'occupied' | 'reserved' | 'dirty';
  capacity: number;
  qr_code_url?: string;
  created_at: string;
  updated_at: string;
}

export interface MenuItem {
  id: string;
  restaurant_id: string;
  menu_id: string;
  name: string;
  description?: string;
  image_url?: string;
  category: string;
  base_price: number;
  current_price: number;
  is_available: boolean;
  is_featured: boolean;
  created_at: string;
  updated_at: string;
  dietary_info?: string[];
  allergens?: string[];
}

export interface Menu {
  id: string;
  restaurant_id: string;
  name: string;
  description?: string;
  is_active: boolean;
  start_time?: string;
  end_time?: string;
  days_available?: string[];
  created_at: string;
  updated_at: string;
}

export interface TrafficData {
  id: string;
  restaurant_id: string;
  timestamp: string;
  table_occupancy: number;
  total_tables: number;
  occupancy_percentage: number;
  customer_count: number;
  created_at: string;
  data_source: 'sensor' | 'manual' | 'estimated';
}

export interface RestaurantActivity {
  occupancyRate: number;
  previousOccupancyRate: number;
  totalSales: number;
  previousTotalSales: number;
  ordersCount: number;
  previousOrdersCount: number;
  avgOrderValue: number;
  previousAvgOrderValue: number;
  timestamp: string;
}

export interface CustomerFeedback {
  id: string;
  order_id: string;
  restaurant_id: string;
  table_id?: string;
  food_rating: number;
  service_rating: number;
  app_rating: number;
  comments: string;
  customer_email?: string;
  is_declined?: boolean;
  created_at: string;
  sentiment_score?: number;
  sentiment_magnitude?: number;
}
