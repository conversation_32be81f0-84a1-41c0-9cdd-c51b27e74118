/**
 * Order Status Service
 * Provides real-time order status tracking using Supabase Realtime
 */

import { supabase } from '@/integrations/supabase/client';
import { Order, OrderStatus } from '../types';
import { storeOrderInLocalHistory } from './orderHistoryService';

/**
 * Setup a real-time listener for order status changes
 * @param orderId Order ID to track
 * @param callback Function to call when status changes
 * @returns Cleanup function to unsubscribe
 */
export function setupOrderStatusListener(
  orderId: string, 
  callback: (order: Order) => void
): () => void {
  // Validate orderId before attempting to set up the listener
  if (!orderId || orderId === 'undefined' || orderId === 'null') {
    console.error('🔔 REALTIME SUBSCRIPTION ERROR: Invalid order ID:', orderId);
    // Return a no-op cleanup function
    return () => console.log('🔔 No channel to clean up (invalid order ID)');
  }
  
  // Validate UUID format
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  if (!uuidRegex.test(orderId)) {
    console.error('🔔 REALTIME SUBSCRIPTION ERROR: Order ID not in UUID format:', orderId);
    return () => console.log('🔔 No channel to clean up (invalid UUID format)');
  }
  
  console.log('%c🔔 SETTING UP REALTIME ORDER STATUS LISTENER', 'background: #008080; color: white; padding: 2px; font-weight: bold');
  console.log('🔔 Order ID:', orderId);
  console.log('🔔 Channel name:', `order-${orderId}`);
  
  // Create a channel specific to this order
  const channel = supabase
    .channel(`order-${orderId}`)
    .on(
      'postgres_changes',
      {
        event: 'UPDATE',
        schema: 'public',
        table: 'orders',
        filter: `id=eq.${orderId}`
      },
      (payload) => {
        console.log('%c🔔 REALTIME UPDATE RECEIVED!', 'background: #008080; color: white; padding: 2px; font-weight: bold');
        console.log('🔔 Payload type:', payload.eventType);
        console.log('🔔 Full payload:', payload);
        
        // The database response doesn't include items but our Order type requires it
        // Create a DbOrder type that matches what comes from the database
        const updatedOrder = payload.new as unknown as {
          id: string;
          table_id: string;
          restaurant_id: string;
          status: OrderStatus;
          total_amount: number;
          created_at: string;
          updated_at: string;
          customer_session_id: string;
          special_requests: string;
          completed_at: string;
        };
        
        console.log('🔔 Status changed to:', updatedOrder.status);
        
        // Convert to our application Order type by adding empty items array
        const fullOrder: Order = {
          ...updatedOrder,
          status: updatedOrder.status as OrderStatus, // Explicitly cast the status to OrderStatus
          items: [] // We don't have items in the real-time update, would need a separate query to get them
        };
        
        // Update local storage with the latest order status
        storeOrderInLocalHistory(fullOrder);
        
        // Call the callback with the updated order
        callback(fullOrder);
      }
    )
    .subscribe((status) => {
      console.log('🔔 Subscription status:', status);
    });

  // Return a cleanup function that removes the channel when called
  return () => {
    supabase.removeChannel(channel);
  };
}

/**
 * Fetch the current status of an order
 * @param orderId Order ID to fetch
 * @returns Promise resolving to the order or null if not found
 */
export async function fetchOrderStatus(orderId: string): Promise<Order | null> {
  // Validate orderId is a valid UUID before making the request
  if (!orderId || orderId === 'undefined' || orderId === 'null') {
    console.error('🔴 Invalid order ID provided to fetchOrderStatus:', orderId);
    return null;
  }
  
  // Validate UUID format using regex
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  if (!uuidRegex.test(orderId)) {
    console.error('🔴 Order ID is not a valid UUID format:', orderId);
    return null;
  }
  
  try {
    const { data, error } = await supabase
      .from('orders')
      .select('*')
      .eq('id', orderId)
      .single();

    if (error) {
      console.error('Error fetching order status:', error);
      return null;
    }

    if (data) {
      console.log('%c🔔 ORDER STATUS - FETCH SINGLE', 'background: #008080; color: white; padding: 2px; font-weight: bold');
      console.log('🔔 Fetched order data:', data);

      // Fetch order items for this order
      const { data: orderItems, error: itemsError } = await supabase
        .from('order_items')
        .select(`
          *,
          menu_items (id, name, description, category, current_price, image_url)
        `)
        .eq('order_id', orderId);

      if (itemsError) {
        console.error('Error fetching order items for order', orderId, ':', itemsError);
      }

      // Convert order items to the expected format
      const items = orderItems ? orderItems.map(item => ({
        id: item.id,
        name: item.menu_items?.name || 'Unknown Item',
        price: item.unit_price,
        quantity: item.quantity,
        notes: item.special_instructions || undefined
      })) : [];

      // Convert database response to our application Order type
      const fullOrder: Order = {
        ...data,
        status: data.status as OrderStatus, // Explicitly cast the status to OrderStatus
        items: items
      };

      // Update local history with the latest data
      storeOrderInLocalHistory(fullOrder);

      return fullOrder;
    }

    return null;
  } catch (error) {
    console.error('Failed to fetch order status:', error);
    return null;
  }
}

/**
 * Fetch all active orders for a table
 * @param tableId Table ID to fetch orders for
 * @returns Promise resolving to an array of active orders
 */
export async function fetchActiveOrdersForTable(tableId: string): Promise<Order[]> {
  try {
    const { data, error } = await supabase
      .from('orders')
      .select('*')
      .eq('table_id', tableId)
      .not('status', 'in', '("completed","cancelled")')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching active orders:', error);
      return [];
    }

    console.log('%c🔔 ORDER STATUS - FETCH ACTIVE ORDERS', 'background: #008080; color: white; padding: 2px; font-weight: bold');
    console.log('🔔 Fetched active orders:', data?.length || 0);

    // Convert database response to properly typed Order objects and fetch items for each order
    const fullOrders = data ? await Promise.all(data.map(async orderData => {
      // Fetch order items for this order
      const { data: orderItems, error: itemsError } = await supabase
        .from('order_items')
        .select(`
          *,
          menu_items (id, name, description, category, current_price, image_url)
        `)
        .eq('order_id', orderData.id);

      if (itemsError) {
        console.error('Error fetching order items for order', orderData.id, ':', itemsError);
      }

      // Convert order items to the expected format
      const items = orderItems ? orderItems.map(item => ({
        id: item.id,
        name: item.menu_items?.name || 'Unknown Item',
        price: item.unit_price,
        quantity: item.quantity,
        notes: item.special_instructions || undefined
      })) : [];

      const fullOrder: Order = {
        ...orderData,
        status: orderData.status as OrderStatus,
        items: items
      };

      // Store in local history
      storeOrderInLocalHistory(fullOrder);

      return fullOrder;
    })) : [];

    return fullOrders;
  } catch (error) {
    console.error('Failed to fetch active orders:', error);
    return [];
  }
}

/**
 * Fetch all orders for a table (including completed and cancelled)
 * @param tableId Table ID to fetch orders for
 * @returns Promise resolving to an array of all orders
 */
export async function fetchAllOrdersForTable(tableId: string): Promise<Order[]> {
  try {
    const { data, error } = await supabase
      .from('orders')
      .select('*')
      .eq('table_id', tableId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching all orders:', error);
      return [];
    }

    console.log('%c🔔 ORDER STATUS - FETCH ALL ORDERS', 'background: #008080; color: white; padding: 2px; font-weight: bold');
    console.log('🔔 Fetched all orders:', data?.length || 0);

    // Convert database response to properly typed Order objects and fetch items for each order
    const fullOrders = data ? await Promise.all(data.map(async orderData => {
      // Fetch order items for this order
      const { data: orderItems, error: itemsError } = await supabase
        .from('order_items')
        .select(`
          *,
          menu_items (id, name, description, category, current_price, image_url)
        `)
        .eq('order_id', orderData.id);

      if (itemsError) {
        console.error('Error fetching order items for order', orderData.id, ':', itemsError);
      }

      // Convert order items to the expected format
      const items = orderItems ? orderItems.map(item => ({
        id: item.id,
        name: item.menu_items?.name || 'Unknown Item',
        price: item.unit_price,
        quantity: item.quantity,
        notes: item.special_instructions || undefined
      })) : [];

      const fullOrder: Order = {
        ...orderData,
        status: orderData.status as OrderStatus,
        items: items
      };

      // Store in local history
      storeOrderInLocalHistory(fullOrder);

      return fullOrder;
    })) : [];

    return fullOrders;
  } catch (error) {
    console.error('Failed to fetch all orders:', error);
    return [];
  }
}

/**
 * Get a friendly display message based on order status
 * @param status Order status
 * @returns Human-readable status message
 */
export function getOrderStatusMessage(status: OrderStatus): string {
  switch(status.toLowerCase()) {
    case 'new':
      return 'Order received - waiting for kitchen confirmation';
    case 'preparing':
      return 'Your order is being prepared in the kitchen';
    case 'ready':
      return 'Your order is ready for pickup or delivery to your table';
    case 'delivered':
      return 'Order has been delivered to your table';
    case 'completed':
      return 'Order completed - thank you for dining with us!';
    case 'cancelled':
      return 'Order has been cancelled';
    default:
      return 'Order status unknown';
  }
}
