import { supabase } from '@/integrations/supabase/client';
import { getRestaurantId } from './restaurantDbService';
import { User } from '@supabase/supabase-js';

// Define proper MenuItem interface to replace 'any' types
export interface MenuItem {
  id: string;
  name: string;
  description?: string;
  price: number;
  base_price: number;
  current_price: number;
  menu_id: string;
  restaurant_id: string;
  // user_id field doesn't exist in the menu_items table
  category?: string;
  is_available: boolean;
  image_url?: string;
  created_at?: string;
  updated_at?: string;
}

// Define a type for creating a menu item (without the id and timestamps)
export type MenuItemInput = Omit<MenuItem, 'id' | 'created_at' | 'updated_at'>;

// Fetch all menu items for a restaurant
export const fetchAllMenuItems = async (user?: User | null) => {
  try {
    const restaurantId = await getRestaurantId(user);
    console.log('Fetching menu items for restaurant ID:', restaurantId);

    // First, get all menus for this restaurant
    const { data: menus, error: menuError } = await supabase
      .from('menus')
      .select('id')
      .eq('restaurant_id', restaurantId);

    if (menuError) {
      console.error('Error fetching menus:', menuError);
      throw menuError;
    }

    if (!menus || menus.length === 0) {
      console.log('No menus found for this restaurant');
      return [];
    }

    const menuIds = menus.map(menu => menu.id);
    console.log('Found menu IDs:', menuIds);

    // Then get all menu items for these menus
    const { data, error } = await supabase
      .from('menu_items')
      .select('*')
      .in('menu_id', menuIds)
      .order('category')
      .order('name');

    if (error) {
      console.error('Error fetching menu items:', error);
      throw error;
    }

    console.log(`Found ${data?.length || 0} menu items`);
    return data || [];
  } catch (error) {
    console.error('Error fetching all menu items:', error);
    return [];
  }
};

// Fetch all menu items for a specific menu
export const fetchMenuItems = async (menuId: string) => {
  try {
    const { data, error } = await supabase
      .from('menu_items')
      .select('*')
      .eq('menu_id', menuId)
      .order('name');

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error fetching menu items:', error);
    throw error;
  }
};

// Fetch a single menu item by ID
export const fetchMenuItemById = async (itemId: string) => {
  try {
    const { data, error } = await supabase
      .from('menu_items')
      .select('*')
      .eq('id', itemId)
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error fetching menu item by ID:', error);
    throw error;
  }
};

// Create a new menu item
export const createMenuItem = async (menuItemData: Partial<MenuItemInput>): Promise<MenuItem> => {
  try {
    if (!menuItemData.menu_id) {
      throw new Error('Menu ID is required to create a menu item');
    }

    // Check if a menu item with this name already exists for this menu
    const { data: existingItems, error: checkError } = await supabase
      .from('menu_items')
      .select('id, name')
      .eq('menu_id', menuItemData.menu_id)
      .eq('name', menuItemData.name);

    if (checkError) {
      console.error('Error checking for existing menu item:', checkError);
    } else if (existingItems && existingItems.length > 0) {
      console.log(`Menu item "${menuItemData.name}" already exists for this menu, skipping creation`);
      // Fetch the complete menu item to return with all fields
      const { data: fullItem, error: fetchError } = await supabase
        .from('menu_items')
        .select('*')
        .eq('id', existingItems[0].id)
        .single();
        
      if (fetchError) {
        console.error('Error fetching complete menu item:', fetchError);
        throw fetchError;
      }
      
      return fullItem as MenuItem;
    }

    // Ensure price and current_price are set correctly
    const itemDataWithDefaults = {
      ...menuItemData,
      // If price exists but current_price doesn't, set current_price = price
      current_price: menuItemData.current_price || menuItemData.price || 0,
      // Ensure price is set to avoid other constraint violations
      price: menuItemData.price || 0,
      // Set creation timestamps
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    
    console.log('Creating menu item with data:', {
      name: itemDataWithDefaults.name,
      category: itemDataWithDefaults.category,
      menu_id: itemDataWithDefaults.menu_id,
      price: itemDataWithDefaults.price,
      current_price: itemDataWithDefaults.current_price
    });

    const { data, error } = await supabase
      .from('menu_items')
      .insert(itemDataWithDefaults)
      .select()
      .single();

    if (error) {
      console.error('Error in Supabase insert operation:', error);
      console.error('Error details:', {
        message: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code
      });
      throw error;
    }

    console.log('Successfully created menu item:', data.name, 'with ID:', data.id);
    return data;
  } catch (error) {
    console.error('Error creating menu item:', error);
    throw error;
  }
};

// Update an existing menu item
export const updateMenuItem = async (itemId: string, menuItemData: Partial<MenuItem>): Promise<MenuItem | null> => {
  try {
    const { data, error } = await supabase
      .from('menu_items')
      .update({
        ...menuItemData,
        updated_at: new Date().toISOString()
      })
      .eq('id', itemId)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error updating menu item:', error);
    throw error;
  }
};

// Delete a menu item
export const deleteMenuItem = async (itemId: string) => {
  try {
    const { error } = await supabase
      .from('menu_items')
      .delete()
      .eq('id', itemId);

    if (error) throw error;
    return true;
  } catch (error) {
    console.error('Error deleting menu item:', error);
    throw error;
  }
};
