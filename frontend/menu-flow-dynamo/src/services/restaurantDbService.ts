import { supabase } from '@/integrations/supabase/client';
import { User } from '@supabase/supabase-js';

interface RestaurantData {
  name: string;
  address: string;
  contact_email: string;
  user_id?: string; // user_id is added separately in createRestaurant
  created_at?: string;
  updated_at?: string;
  // Add other potential restaurant properties here
}

// We don't use a hardcoded default restaurant ID anymore
// Instead, we'll determine the restaurant dynamically based on the table ID

// Get restaurant by ID - now properly gets restaurant_details with business info
export const getRestaurantById = async (id: string) => {
  try {
    // Validate input
    if (!id) {
      console.error('Cannot fetch restaurant: No ID provided');
      return null;
    }

    console.log(`Fetching restaurant with ID: ${id}`);

    // First try to get restaurant_details (correct approach)
    const { data: restaurantDetails, error: detailsError } = await supabase
      .from('restaurant_details')
      .select(`
        id,
        business_id,
        cuisine_type,
        seating_capacity,
        operating_hours,
        dynamic_pricing_enabled,
        pricing_rules,
        businesses!inner(
          id,
          name,
          address,
          email,
          phone,
          description,
          logo_url,
          user_id
        )
      `)
      .eq('id', id)
      .single();

    if (!detailsError && restaurantDetails) {
      console.log(`Successfully fetched restaurant details: ${restaurantDetails.businesses.name} (ID: ${restaurantDetails.id})`);
      // Return in the format expected by the frontend
      return {
        id: restaurantDetails.id, // restaurant_details.id
        name: restaurantDetails.businesses.name,
        address: restaurantDetails.businesses.address,
        contact_email: restaurantDetails.businesses.email,
        contact_phone: restaurantDetails.businesses.phone,
        description: restaurantDetails.businesses.description,
        logo_url: restaurantDetails.businesses.logo_url,
        opening_hours: restaurantDetails.operating_hours,
        dynamic_pricing_enabled: restaurantDetails.dynamic_pricing_enabled,
        pricing_rules: restaurantDetails.pricing_rules,
        user_id: restaurantDetails.businesses.user_id
      };
    }

    // Fallback: try to get business and create restaurant_details if needed
    console.log('Restaurant details not found, checking if this is a business ID...');
    const { data: business, error: businessError } = await supabase
      .from('businesses')
      .select('*')
      .eq('id', id)
      .single();

    if (businessError || !business) {
      console.warn(`No restaurant or business found with ID: ${id}`);
      return null;
    }

    // Check if this business has restaurant_details
    const { data: existingDetails } = await supabase
      .from('restaurant_details')
      .select('id')
      .eq('business_id', business.id)
      .single();

    if (existingDetails) {
      // Recursively call with the correct restaurant_details.id
      return getRestaurantById(existingDetails.id);
    }

    console.log(`Business found but no restaurant details. Business: ${business.name} (ID: ${business.id})`);
    return {
      id: business.id, // This is wrong but kept for backward compatibility
      name: business.name,
      address: business.address,
      contact_email: business.email,
      contact_phone: business.phone,
      description: business.description,
      logo_url: business.logo_url,
      user_id: business.user_id
    };
  } catch (error) {
    console.error('Error fetching restaurant:', error);
    return null;
  }
};

// Get restaurant by user ID
export const getRestaurantByUserId = async (userId: string) => {
  try {
    const { data, error } = await supabase
      .from('businesses')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error fetching restaurant by user ID:', error);
    return null;
  }
};

// Create a new restaurant
export const createRestaurant = async (restaurantData: RestaurantData, user: User) => {
  try {
    const { data, error } = await supabase
      .from('businesses')
      .insert({
        name: restaurantData.name,
        address: restaurantData.address,
        email: restaurantData.contact_email, // mapped to email field in businesses table
        user_id: user.id,
        business_type_id: 'f4b7c123-4567-8901-2345-6789abcdef01', // Default restaurant type ID - you may need to adjust this
        ...restaurantData // Include other optional properties
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error creating restaurant:', error);
    throw error;
  }
};

// Update restaurant details
export const updateRestaurant = async (id: string, restaurantData: RestaurantData) => {
  try {
    console.log('Updating restaurant with ID:', id);
    console.log('Update data:', restaurantData);

    // First check if the restaurant exists
    const { data: existingRestaurant, error: checkError } = await supabase
      .from('businesses')
      .select('*')
      .eq('id', id)
      .single();

    if (checkError) {
      console.error('Error checking if restaurant exists:', checkError);
      throw new Error(`Restaurant with ID ${id} not found: ${checkError.message}`);
    }

    console.log('Existing restaurant data:', existingRestaurant);

    // Prepare update data
    const updateData = {
      ...restaurantData,
      updated_at: new Date().toISOString()
    };

    // Make sure required fields are present
    if (!updateData.address && existingRestaurant.address) {
      updateData.address = existingRestaurant.address;
    }

    if (!updateData.contact_email && existingRestaurant.contact_email) {
      updateData.contact_email = existingRestaurant.contact_email;
    }

    if (!updateData.user_id && existingRestaurant.user_id) {
      updateData.user_id = existingRestaurant.user_id;
    }

    console.log('Final update data:', updateData);

    const { data, error } = await supabase
      .from('businesses')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error in Supabase update operation:', error);
      throw error;
    }

    console.log('Successfully updated restaurant:', data);
    return data;
  } catch (error) {
    console.error('Error updating restaurant:', error);
    throw error;
  }
};

// Cache for restaurant IDs to improve performance
let cachedRestaurantId: string | null = null;
let cachedUserId: string | null = null;

// Helper function to get the current restaurant ID (now returns restaurant_details.id)
export const getRestaurantId = async (user?: User | null, tableId?: string): Promise<string | null> => {
  // Check cache first if we have a user and the user ID matches the cached one
  if (cachedRestaurantId && user && cachedUserId === user.id) {
    console.log(`Using cached restaurant ID: ${cachedRestaurantId} for user: ${user.id}`);
    return cachedRestaurantId;
  }

  // First, try to get the restaurant ID from the table ID if provided
  if (tableId) {
    try {
      console.log('Trying to get restaurant ID from table ID:', tableId);
      // Use .single() to fetch a single record
      const { data, error } = await supabase
        .from('restaurant_tables')
        .select('restaurant_id')
        .eq('id', tableId)
        .single();

      if (error) {
        console.error('Error querying restaurant_tables by table ID:', error.message);
        // Continue to try getting restaurant ID from user if table query fails
      } else if (data?.restaurant_id) {
        console.log(`Found restaurant ID ${data.restaurant_id} from table ID ${tableId}`);
        // Cache the result if we have a user
        if (user) {
          cachedRestaurantId = data.restaurant_id;
          cachedUserId = user.id;
        }
        return data.restaurant_id;
      } else {
        console.warn(`Table ID "${tableId}" found, but no restaurant_id associated.`);
      }
    } catch (error) {
      console.error('Error getting restaurant ID from table ID:', error);
      // Continue to try getting restaurant ID from user if this fails
    }
  }

  // If no table ID or table lookup failed, get restaurant_details.id from user's business
  if (user) {
    try {
      console.log('Trying to get restaurant_details ID from user ID:', user.id);
      const { data, error } = await supabase
        .from('restaurant_details')
        .select(`
          id,
          businesses!inner(
            id,
            user_id,
            name
          )
        `)
        .eq('businesses.user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(1);

      if (error) {
        console.error('Error querying restaurant_details by user ID:', error.message);
      } else if (data && data.length > 0) {
        const restaurantDetailsId = data[0].id;
        console.log(`Found restaurant_details ID ${restaurantDetailsId} for user ${user.id}`);
        // Cache the result
        cachedRestaurantId = restaurantDetailsId;
        cachedUserId = user.id;
        return restaurantDetailsId;
      } else {
        console.log('No restaurant_details found for user. Checking businesses...');

        // Fallback: check if user has a business and create restaurant_details if needed
        const { data: businesses, error: businessError } = await supabase
          .from('businesses')
          .select('id, name')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false })
          .limit(1);

        if (!businessError && businesses && businesses.length > 0) {
          const business = businesses[0];
          console.log(`Found business ${business.name} (${business.id}) for user, but no restaurant_details`);

          // For now, return the business ID (this maintains backward compatibility)
          // TODO: In the future, we should create restaurant_details automatically
          cachedRestaurantId = business.id;
          cachedUserId = user.id;
          return business.id;
        }
      }
    } catch (error) {
      console.error('Unexpected error in getRestaurantId:', error);
    }
  }

  // If we couldn't determine the restaurant ID, return null
  console.warn('Could not determine restaurant ID: No user provided or no restaurant found');
  return null;
};
