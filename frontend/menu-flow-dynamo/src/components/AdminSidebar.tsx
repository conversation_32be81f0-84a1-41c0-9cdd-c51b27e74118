
import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import {
  BarChart2,
  Settings,
  Utensils,
  QrCode,
  Clock,
  LogOut,
  PieChart,
  BookOpen,
  Coffee,
  Database,
  Bell,
  MessageSquare,
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useLanguage } from '@/contexts/LanguageContext';
import { useRestaurant } from '@/contexts/RestaurantContext';

const AdminSidebar = () => {
  const { signOut, user } = useAuth();
  const { t } = useLanguage();
  const { restaurantInfo } = useRestaurant();
  const location = useLocation();

  const handleSignOut = async () => {
    await signOut();
  };

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  return (
    <aside className="w-64 bg-white border-r border-gray-200 h-screen sticky top-0">
      <div className="p-4 border-b">
        <h1 className="text-xl font-semibold text-restaurant-primary">
          {restaurantInfo?.name || 'Restaurant'}
        </h1>
        <p className="text-xs text-gray-500">Owner Dashboard</p>
        {user && <p className="text-xs text-gray-500 mt-1 truncate">{user.email}</p>}
      </div>

      <nav className="p-2">
        <div className="mb-2 px-2 py-1.5 text-xs font-semibold text-gray-500">
          MAIN
        </div>
        <div className="space-y-1">
          <Link to="/admin/dashboard">
            <Button
              variant="ghost"
              className={`w-full justify-start ${isActive('/admin/dashboard') ? 'bg-gray-100 text-restaurant-text' : 'text-restaurant-muted'}`}
            >
              <PieChart className="h-4 w-4 mr-3" />
              {t('dashboard')}
            </Button>
          </Link>
          <Link to="/admin/menus">
            <Button
              variant="ghost"
              className={`w-full justify-start ${isActive('/admin/menus') ? 'bg-gray-100 text-restaurant-text' : 'text-restaurant-muted'}`}
            >
              <BookOpen className="h-4 w-4 mr-3" />
              Menus
            </Button>
          </Link>
          <Link to="/admin/menu-items">
            <Button
              variant="ghost"
              className={`w-full justify-start ${isActive('/admin/menu-items') ? 'bg-gray-100 text-restaurant-text' : 'text-restaurant-muted'}`}
            >
              <Coffee className="h-4 w-4 mr-3" />
              Menu Items
            </Button>
          </Link>
          <Link to="/admin/tables">
            <Button
              variant="ghost"
              className={`w-full justify-start ${isActive('/admin/tables') ? 'bg-gray-100 text-restaurant-text' : 'text-restaurant-muted'}`}
            >
              <QrCode className="h-4 w-4 mr-3" />
              {t('tableManagement')}
            </Button>
          </Link>
          <Link to="/admin/orders">
            <Button
              variant="ghost"
              className={`w-full justify-start ${isActive('/admin/orders') ? 'bg-gray-100 text-restaurant-text' : 'text-restaurant-muted'}`}
            >
              <Clock className="h-4 w-4 mr-3" />
              {t('orderManagement')}
            </Button>
          </Link>
          <Link to="/admin/notifications">
            <Button
              variant="ghost"
              className={`w-full justify-start ${isActive('/admin/notifications') ? 'bg-gray-100 text-restaurant-text' : 'text-restaurant-muted'}`}
            >
              <Bell className="h-4 w-4 mr-3" />
              Notifications
            </Button>
          </Link>
          <Link to="/admin/staff-requests">
            <Button
              variant="ghost"
              className={`w-full justify-start ${isActive('/admin/staff-requests') ? 'bg-gray-100 text-restaurant-text' : 'text-restaurant-muted'}`}
            >
              <MessageSquare className="h-4 w-4 mr-3" />
              Staff Requests
            </Button>
          </Link>
          <Link to="/admin/analytics">
            <Button
              variant="ghost"
              className={`w-full justify-start ${isActive('/admin/analytics') ? 'bg-gray-100 text-restaurant-text' : 'text-restaurant-muted'}`}
            >
              <BarChart2 className="h-4 w-4 mr-3" />
              {t('analytics')}
            </Button>
          </Link>
          <Link to="/admin/settings">
            <Button
              variant="ghost"
              className={`w-full justify-start ${isActive('/admin/settings') ? 'bg-gray-100 text-restaurant-text' : 'text-restaurant-muted'}`}
            >
              <Settings className="h-4 w-4 mr-3" />
              {t('settings')}
            </Button>
          </Link>
          {/* Test Data button removed */}
        </div>

        <div className="absolute bottom-4 left-2 right-2">
          <Button
            variant="ghost"
            className="w-full justify-start text-restaurant-muted"
            onClick={handleSignOut}
          >
            <LogOut className="h-4 w-4 mr-3" />
            {t('signOut')}
          </Button>
        </div>
      </nav>
    </aside>
  );
};

export default AdminSidebar;
