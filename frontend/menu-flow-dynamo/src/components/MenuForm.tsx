import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { useToast } from '@/components/ui/use-toast';
import { useLanguage } from '@/contexts/LanguageContext';
import { useAuth } from '@/contexts/AuthContext';
import { useRestaurant } from '@/contexts/RestaurantContext';
import { createMenu, updateMenu, MenuData } from '@/services/menuDbService';

interface MenuFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  editingMenu?: {
    id: string;
    name: string;
    description?: string;
    is_active: boolean;
    start_time?: string;
    end_time?: string;
    days_available?: number[];
  };
}

interface MenuFormData {
  name: string;
  description: string;
  is_active: boolean;
  // Database fields for time-based menu availability
  start_time: string;
  end_time: string;
  days_available: number[];
  restaurant_id?: string; // Added to match database schema
}

const MenuForm: React.FC<MenuFormProps> = ({ isOpen, onClose, onSuccess, editingMenu }) => {
  const { t } = useLanguage();
  const { toast } = useToast();
  const { user } = useAuth();
  const { restaurantInfo } = useRestaurant();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { register, handleSubmit, reset, setValue, watch, formState: { errors } } = useForm<MenuFormData>({
    defaultValues: editingMenu ? {
      name: editingMenu.name,
      description: editingMenu.description || '',
      is_active: editingMenu.is_active,
      start_time: editingMenu.start_time || '',
      end_time: editingMenu.end_time || '',
      days_available: editingMenu.days_available || [],
    } : {
      name: '',
      description: '',
      is_active: true,
      start_time: '12:00',
      end_time: '23:00',
      days_available: [1, 2, 3, 4, 5, 6, 7], // Default to all days
    }
  });

  const onSubmit = async (data: MenuFormData) => {
    try {
      setIsSubmitting(true);

      // Prepare menu data for database
      const menuData: MenuData = {
        name: data.name,
        description: data.description,
        is_active: data.is_active,
        start_time: data.start_time,
        end_time: data.end_time,
        // Keep as number array to match database INTEGER[] type
        days_available: data.days_available || [1, 2, 3, 4, 5, 6, 7],
        restaurant_id: restaurantInfo.id
      };

      console.log(`Using time range: ${menuData.start_time} - ${menuData.end_time}`);

      if (!user) {
        throw new Error('You must be logged in to perform this action');
      }

      let result;
      if (editingMenu) {
        // Update existing menu in database
        result = await updateMenu(editingMenu.id, menuData, restaurantInfo, user);

        if (!result) {
          throw new Error('Failed to update menu - no data returned');
        }

        toast({
          title: "Menu updated",
          description: `${data.name} has been updated successfully.`,
        });
      } else {
        // Create new menu in database
        result = await createMenu(menuData, restaurantInfo, user);

        if (!result) {
          throw new Error('Failed to create menu - no data returned');
        }

        toast({
          title: "Menu created",
          description: `${data.name} has been created successfully.`,
        });
      }

      // Ensure the success callback is called before closing
      console.log('Menu operation successful, calling onSuccess callback');
      await onSuccess();

      // Add a small delay to ensure the refetch completes
      await new Promise(resolve => setTimeout(resolve, 100));

      onClose();
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      console.error('Menu form submission error:', err);
      toast({
        variant: "destructive",
        title: "Error",
        description: errorMessage,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            {editingMenu ? `Edit ${editingMenu.name}` : t('addMenu')}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">{t('name')}</Label>
            <Input
              id="name"
              {...register('name', { required: 'Name is required' })}
            />
            {errors.name && (
              <p className="text-sm text-red-500">{errors.name.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">{t('description')}</Label>
            <Textarea
              id="description"
              {...register('description')}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="start_time">{t('startTime')}</Label>
            <Input
              id="start_time"
              type="time"
              {...register('start_time')}
            />
            <p className="text-sm text-muted-foreground">
              When this menu becomes available
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="end_time">{t('endTime')}</Label>
            <Input
              id="end_time"
              type="time"
              {...register('end_time')}
            />
            <p className="text-sm text-muted-foreground">
              When this menu stops being available
            </p>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="is_active"
              checked={watch('is_active')}
              onCheckedChange={(checked) => setValue('is_active', checked)}
            />
            <Label htmlFor="is_active">Active</Label>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Saving...' : editingMenu ? 'Update Menu' : 'Add Menu'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default MenuForm;
