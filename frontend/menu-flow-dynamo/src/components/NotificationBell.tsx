import React, { useState, useEffect, useRef } from 'react';
import { Bell } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { useRestaurant } from '@/contexts/RestaurantContext';
import { fetchNotifications, setupPushNotifications, checkNotificationsTableExists } from '@/services/notificationService';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { RealtimeChannel } from '@supabase/supabase-js';
import ErrorBoundary from './ErrorBoundary';

import { useNavigate } from 'react-router-dom';
import { markAsReadNotification } from '@/services/notificationService';

export function NotificationBell() {
  return (
    <ErrorBoundary>
      <NotificationBellComponent />
    </ErrorBoundary>
  );
}

function NotificationBellComponent() {
  const { user } = useAuth();
  const { restaurantInfo } = useRestaurant();
  const [showDropdown, setShowDropdown] = useState(false);

  const navigate = useNavigate();
  const orderAudio = useRef(new Audio('/sounds/new-order.mp3'));
  useEffect(() => {
    orderAudio.current.volume = 0.5;
    orderAudio.current.load();
  }, []);

  useEffect(() => {
    if (showDropdown) {
      notifications.filter(n => !n.read).forEach(n => {
        markAsReadNotification(n.id).catch(err => console.error(err));
      });
      refetch();
    }
  }, [showDropdown]);

  // Setup notification permissions when component mounts
  useEffect(() => {
    if (user) {
      setupPushNotifications()
        .then(granted => {
          console.log('Push notifications permission:', granted ? 'granted' : 'denied');
        });
    }
  }, [user]);

  // Fetch notifications from the database
  const { data: notifications = [], refetch } = useQuery({
    queryKey: ['notifications', user?.id, restaurantInfo?.id],
    queryFn: async () => {
      const notifs = await fetchNotifications(user);
      console.log(`Fetched ${notifs.length} notifications, unread: ${notifs.filter(n => !n.read).length}`);
      return notifs;
    },
    refetchInterval: 5000, // Refetch more frequently (every 5 seconds)
    staleTime: 3000, // Consider data stale after 3 seconds to ensure we refresh often
    enabled: !!user
  });

  // State to track previous notification count for sound alerts
  const [prevOrderCount, setPrevOrderCount] = useState(0);

  // Get count of unread order notifications
  const newOrderCount = notifications.filter(n => n.type === 'order' && !n.read).length;

  // Play sound when a new order comes in
  useEffect(() => {
    // Only play sound if order count increased
    if (newOrderCount > prevOrderCount && prevOrderCount >= 0) {
      try {
        console.log('Playing new order sound alert');
        orderAudio.current.play().catch(err => console.log('Audio play failed:', err));
      } catch (error) {
        console.error('Error playing notification sound:', error);
      }
    }
    // Update previous count
    setPrevOrderCount(newOrderCount);
  }, [newOrderCount, prevOrderCount]);

  // Setup real-time notification listener
  useEffect(() => {
    // Only proceed if we have both user and a valid restaurant ID
    if (!user || !restaurantInfo?.id) return;

    // Create unique channel names for this restaurant to prevent duplicates
    const postgresChannelName = `postgres-changes-${restaurantInfo.id}-${Date.now()}`;
    const broadcastChannelName = `broadcast-channel-${restaurantInfo.id}-${Date.now()}`;
    let postgresChannel: RealtimeChannel | null = null;
    let broadcastChannel: RealtimeChannel | null = null;

    // First check if notifications table exists
    const setupChannels = async () => {
      try {
        // Check if notifications table exists
        const exists = await checkNotificationsTableExists();
        if (!exists) {
          console.log('Notifications table does not exist - skipping real-time listener');
          return;
        }

        console.log(`Setting up notification listeners for restaurant: ${restaurantInfo.id}`);

        // Remove any existing channels to prevent duplicates
        const existingChannels = supabase.getChannels();
        for (const existingChannel of existingChannels) {
          if (existingChannel.topic.includes('postgres-changes-') ||
              existingChannel.topic.includes('broadcast-channel-') ||
              existingChannel.topic.includes('notification-updates-')) {
            console.log(`Removing existing channel: ${existingChannel.topic}`);
            supabase.removeChannel(existingChannel);
          }
        }

        // Handle notification events
        const handleNotification = (notification: any) => {
          console.log('New notification received:', notification);

          // Play sound for new order notifications
          if (notification.type === 'order') {
            try {
              console.log('Playing new order sound alert');
              orderAudio.current.play().catch(err => console.log('Audio play failed:', err));
            } catch (error) {
              console.error('Error playing notification sound:', error);
            }
          }

          // Refresh notifications
          refetch();
        };

        // 1. Create a channel for postgres changes (direct DB inserts)
        postgresChannel = supabase.channel(postgresChannelName)
          .on(
            'postgres_changes',
            {
              event: 'INSERT',
              schema: 'public',
              table: 'notifications',
              filter: `restaurant_id=eq.${restaurantInfo.id}`
            },
            (payload) => {
              console.log('New notification received via postgres changes:', payload);
              if (payload.new) {
                handleNotification(payload.new);
              }
            }
          )
          .subscribe((status) => {
            console.log(`Postgres channel status: ${status}`, postgresChannel?.topic);
          });

        // 2. Create a channel for broadcast events (from Edge Function)
        broadcastChannel = supabase.channel(broadcastChannelName)
          .on(
            'broadcast',
            { event: 'notification' },
            (payload) => {
              console.log('New notification received via broadcast:', payload);

              // Check if this notification is for this restaurant
              if (payload.payload?.notification?.restaurant_id === restaurantInfo.id) {
                handleNotification(payload.payload.notification);
              }
            }
          )
          .subscribe((status) => {
            console.log(`Broadcast channel status: ${status}`, broadcastChannel?.topic);
          });

        // 3. Also subscribe to the custom-insert-channel used by the Edge Function
        const customChannel = supabase.channel('custom-insert-channel')
          .on(
            'broadcast',
            { event: 'notification' },
            (payload) => {
              console.log('New notification received via custom channel:', payload);

              // Check if this notification is for this restaurant
              if (payload.payload?.notification?.restaurant_id === restaurantInfo.id) {
                handleNotification(payload.payload.notification);
              }
            }
          )
          .subscribe((status) => {
            console.log(`Custom channel status: ${status}`);
          });
      } catch (error) {
        console.error('Error setting up notification listeners:', error);
      }
    };

    setupChannels();

    // Clean up function
    return () => {
      if (postgresChannel) {
        console.log(`Cleaning up postgres channel: ${postgresChannelName}`);
        supabase.removeChannel(postgresChannel);
      }
      if (broadcastChannel) {
        console.log(`Cleaning up broadcast channel: ${broadcastChannelName}`);
        supabase.removeChannel(broadcastChannel);
      }

      // Also clean up the custom channel
      const customChannel = supabase.getChannels().find(ch => ch.topic === 'custom-insert-channel');
      if (customChannel) {
        console.log('Cleaning up custom channel');
        supabase.removeChannel(customChannel);
      }
    };
  }, [user, restaurantInfo?.id, refetch]);

  const handleNotificationClick = async (notif) => {
    try {
      console.log('🔔 Notification clicked:', notif);
      console.log('🔔 Current user:', user);
      console.log('🔔 Current restaurant info:', restaurantInfo);

      await markAsReadNotification(notif.id);
      refetch();
      setShowDropdown(false);

      // Derive order ID from payload or action_url
      let orderId = notif.payload?.order_id || notif.order_id;
      if (!orderId && notif.action_url) {
        const parts = notif.action_url.split('?');
        if (parts[1]) {
          const params = new URLSearchParams(parts[1]);
          orderId = params.get('orderId');
        }
      }

      console.log('🔔 Extracted order ID:', orderId);
      console.log('🔔 Navigating to: /admin/orders');

      if (orderId) {
        // Redirect to the admin order management page
        navigate(`/admin/orders`);
      } else {
        // Fallback to the generic admin orders list
        navigate(`/admin/orders`);
      }
    } catch (error) {
      console.error('Error handling notification click:', error);
    }
  };

  return (
    <div className="relative">
      <Button
        variant="ghost"
        size="icon"
        className="relative h-9 w-9 rounded-full hover:bg-sky-100 dark:hover:bg-sky-900/30"
        onClick={() => setShowDropdown(!showDropdown)}
        aria-label="Notifications"
      >
        <Bell className="h-5 w-5 text-gray-700 dark:text-gray-300" />
        {newOrderCount > 0 && (
          <span className="absolute -top-1 -right-1 flex h-5 w-5 items-center justify-center rounded-full bg-sky-600 text-[10px] text-white font-medium">
            {newOrderCount > 99 ? '99+' : newOrderCount}
          </span>
        )}
      </Button>

      {/* Notification dropdown */}
      {showDropdown && (
        <div className="absolute right-0 mt-2 w-80 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 z-50">
          <div className="p-3 border-b border-gray-200 dark:border-gray-700">
            <div className="flex justify-between items-center">
              <h3 className="text-sm font-medium">Notifications</h3>
              {newOrderCount > 0 && (
                <span className="text-xs bg-sky-100 text-sky-800 dark:bg-sky-900 dark:text-sky-200 px-2 py-0.5 rounded-full">
                  {newOrderCount} new
                </span>
              )}
            </div>
          </div>

          <div className="max-h-72 overflow-y-auto">
            {notifications.length > 0 ? (
              <div className="py-1">
                {notifications.slice(0, 5).map((notification) => (
                  <div
                    key={notification.id}
                    onClick={() => handleNotificationClick(notification)}
                    className={`cursor-pointer px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-700 ${!notification.read ? 'bg-sky-50 dark:bg-sky-900/20' : ''}`}
                  >
                    <div className="flex items-start">
                      <div className="ml-3 w-0 flex-1">
                        <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {notification.title}
                        </p>
                        <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                          {notification.message}
                        </p>
                        <div className="mt-1 flex space-x-1">
                          <p className="text-xs text-gray-400">
                            {new Date(notification.timestamp).toLocaleTimeString()}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="px-4 py-6 text-center text-sm text-gray-500 dark:text-gray-400">
                No notifications
              </div>
            )}
          </div>

          <div className="border-t border-gray-200 dark:border-gray-700 p-2">
            <button
              className="w-full px-4 py-2 text-xs text-center text-sky-600 dark:text-sky-400 hover:text-sky-800 dark:hover:text-sky-300"
              onClick={() => setShowDropdown(false)}
            >
              View all notifications
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
