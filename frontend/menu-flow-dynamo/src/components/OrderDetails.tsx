import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, <PERSON><PERSON>Title, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/components/ui/use-toast';
import { format } from 'date-fns';

interface OrderDetailsProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  orderId: string | null;
}

const OrderDetails: React.FC<OrderDetailsProps> = ({ isOpen, onClose, onSuccess, orderId }) => {
  const { toast } = useToast();
  const [order, setOrder] = useState<any>(null);
  const [orderItems, setOrderItems] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [status, setStatus] = useState<string>('');
  const [isUpdating, setIsUpdating] = useState(false);

  useEffect(() => {
    if (isOpen && orderId) {
      fetchOrderDetails();
    }
  }, [isOpen, orderId]);

  const fetchOrderDetails = async () => {
    setIsLoading(true);
    try {
      // Fetch order details
      const { data: orderData, error: orderError } = await supabase
        .from('orders')
        .select(`
          *,
          restaurant_tables (table_number)
        `)
        .eq('id', orderId)
        .single();
      
      if (orderError) throw orderError;
      
      setOrder(orderData);
      setStatus(orderData.status);
      
      // Fetch order items
      const { data: itemsData, error: itemsError } = await supabase
        .from('order_items')
        .select(`
          *,
          menu_items (name, category)
        `)
        .eq('order_id', orderId);
      
      if (itemsError) throw itemsError;
      
      setOrderItems(itemsData || []);
    } catch (err: any) {
      toast({
        variant: "destructive",
        title: "Error",
        description: err.message,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const updateOrderStatus = async () => {
    if (!order || status === order.status) return;

    setIsUpdating(true);
    try {
      console.log('Updating order status:', { orderId, currentStatus: order.status, newStatus: status });

      const { data, error } = await supabase
        .from('orders')
        .update({
          status: status,
          updated_at: new Date().toISOString(),
          ...(status === 'completed' ? { completed_at: new Date().toISOString() } : {})
        })
        .eq('id', orderId)
        .select();

      if (error) {
        console.error('Error updating order status:', error);
        console.error('Error details:', {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code
        });
        throw error;
      }

      console.log('Order status updated successfully:', data);

      toast({
        title: "Order updated",
        description: `Order status changed to ${status}`,
      });

      // Refresh the order details
      await fetchOrderDetails();
      onSuccess();
    } catch (err: any) {
      console.error('Failed to update order status:', err);
      toast({
        variant: "destructive",
        title: "Error updating order",
        description: err.message || 'Failed to update order status',
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-restaurant-primary/20 text-restaurant-text';
      case 'preparing': return 'bg-restaurant-secondary/20 text-restaurant-text';
      case 'ready': return 'bg-green-600/20 text-restaurant-text';
      case 'delivered': return 'bg-restaurant-secondary/30 text-restaurant-text';
      case 'completed': return 'bg-restaurant-primary/30 text-restaurant-text';
      case 'cancelled': return 'bg-destructive/20 text-restaurant-text';
      default: return 'bg-restaurant-primary/10 text-restaurant-text';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Order Details</DialogTitle>
        </DialogHeader>
        
        {isLoading ? (
          <div className="py-8 text-center">Loading order details...</div>
        ) : order ? (
          <div className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Order ID</h3>
                <p className="font-medium">{order.id.substring(0, 8)}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Table</h3>
                <p className="font-medium">{order.restaurant_tables?.table_number || 'N/A'}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Date</h3>
                <p className="font-medium">
                  {order.created_at ? format(new Date(order.created_at), 'MMM dd, yyyy HH:mm') : 'N/A'}
                </p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Status</h3>
                <div className="flex items-center mt-1">
                  <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(order.status)}`}>
                    {order.status}
                  </span>
                </div>
              </div>
            </div>
            
            <Card>
              <CardContent className="p-4">
                <h3 className="font-medium mb-3">Order Items</h3>
                <div className="space-y-3">
                  {orderItems.map((item) => (
                    <div key={item.id} className="flex justify-between border-b pb-2 last:border-0 last:pb-0">
                      <div>
                        <p className="font-medium">{item.menu_items?.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {item.menu_items?.category} • ${item.unit_price.toFixed(2)} each
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">{item.quantity}x</p>
                        <p className="text-sm">${(item.unit_price * item.quantity).toFixed(2)}</p>
                      </div>
                    </div>
                  ))}
                </div>
                
                <div className="flex justify-between mt-4 pt-3 border-t">
                  <p className="font-semibold">Total</p>
                  <p className="font-semibold">${order.total_amount.toFixed(2)}</p>
                </div>
              </CardContent>
            </Card>
            
            <div className="space-y-2">
              <Label htmlFor="status">Update Status</Label>
              <Select value={status} onValueChange={setStatus}>
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="preparing">Preparing</SelectItem>
                  <SelectItem value="ready">Ready</SelectItem>
                  <SelectItem value="delivered">Delivered</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <DialogFooter>
              <Button variant="outline" onClick={onClose}>
                Close
              </Button>
              <Button 
                onClick={updateOrderStatus} 
                disabled={isUpdating || status === order.status}
              >
                {isUpdating ? 'Updating...' : 'Update Status'}
              </Button>
            </DialogFooter>
          </div>
        ) : (
          <div className="py-8 text-center text-muted-foreground">
            Order not found
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default OrderDetails;
